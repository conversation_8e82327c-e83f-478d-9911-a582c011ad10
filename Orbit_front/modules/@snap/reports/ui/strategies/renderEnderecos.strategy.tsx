import React from "react";
import { Endereco } from "../../model/Enderecos";
import { RenderStrategy } from "./RenderStrategy";
import { GridItem } from "../components/GridItem";
import { GridContainer } from "../components/GridContainer";
import { ReadOnlyInputField, CustomLabel } from "@snap/design-system";
import { translatePropToLabel } from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";

export class RenderEnderecos implements RenderStrategy<Endereco> {
  validateKeys = (keys: Array<keyof Endereco>): boolean => {
    return keys.some((campo) => campo in this.formatByKey);
  };

  formatByKey: Record<
    string,
    (enderecos?: Endereco) => React.ReactElement | null
  > = {
      endereco: (enderecos?: Endereco) => {
        if (!enderecos?.detalhes?.length) return null;

        return (
          <GridContainer cols={1} className="">
            <GridItem fullWidth>
              <GridContainer cols={2}>
                {enderecos.detalhes.map((detalhes, enderecoIndex) => (
                  <GridItem key={`endereco-column-${enderecoIndex}`} cols={1}>
                    <div className="mb-4">
                      <CustomLabel
                        label={`${(detalhes.label || `Endereço`).toUpperCase()} ${enderecoIndex + 1
                          }`}
                        colorClass="bg-white"
                      />
                      <div className="pl-5 pt-4">
                        {Object.entries(detalhes.value).map(
                          ([key, valueObj]) =>
                            key !== "label default key" && (
                              <GridItem
                                key={`endereco-${enderecoIndex}-${key}`}
                                cols={1}
                                className="py-2"
                              >
                                <ReadOnlyInputField
                                  label={typeof valueObj === 'object' && valueObj?.label 
                                    ? valueObj.label.toUpperCase() 
                                    : translatePropToLabel(key).toUpperCase()}
                                  value={String(
                                    typeof valueObj === 'object' && valueObj?.value 
                                      ? valueObj.value 
                                      : valueObj
                                  )}
                                  icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                />
                              </GridItem>
                            )
                        )}
                      </div>
                    </div>
                  </GridItem>
                ))}
              </GridContainer>
            </GridItem>
          </GridContainer>
        );
      },
    };

  render = (data: Endereco): React.ReactElement[] => {
    const elementos: React.ReactElement[] = [];

    // Iterate through all keys in the dictionary
    Object.keys(this.formatByKey).forEach((chave) => {
      const elemento = this.formatByKey[chave](data);
      if (elemento) {
        elementos.push(
          <React.Fragment key={`fragment-${chave}`}>{elemento}</React.Fragment>
        );
      }
    });

    return elementos;
  };
}
