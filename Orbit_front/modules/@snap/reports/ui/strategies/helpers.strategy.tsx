import React from "react";
import { ReadOnlyInputField, CustomLabel } from "@snap/design-system";
import { GridItem } from "../components/GridItem";
import { GridContainer } from "../components/GridContainer";
import { translatePropToLabel } from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { REPORT_LABELS, GENERIC_CONSTANTS } from "../../config/constants";

/**
 * Gets the appropriate label for a field based on its structure
 * @param key The field key
 * @param value The field value (which might be an object with a label property)
 * @returns The appropriate label for the field
 */
export const getFieldLabel = (key: string, value: any): string => {
  return typeof value === 'object' && value?.label 
    ? value.label 
    : translatePropToLabel(key);
};

/**
 * Gets the actual value from a field that might be a nested object
 * @param value The field value (which might be an object with a value property)
 * @returns The actual value to display
 */
export const getFieldValue = (value: any): any => {
  return typeof value === 'object' && value?.value !== undefined
    ? value.value
    : value;
};

/**
 * Checks if a value is a valid array of ValueWithSource objects
 * @param value The value to check
 * @returns true if the value is a valid array of ValueWithSource objects, false otherwise
 */
export const isValidArray = (value: any): boolean => {
  if (!Array.isArray(value) || value.length === 0) {
    return false;
  }
  const firstItem = value[0];
  
  return (
    typeof firstItem === 'object' &&
    firstItem !== null &&
    'value' in firstItem &&
    'source' in firstItem &&
    typeof firstItem.value === 'object'
  );
};

/**
 * Generic function to render arrays of ValueWithSource objects with complex value objects
 * @param key The key of the array in the data object
 * @param data The data object containing the array
 * @returns React element or null if no data
 */
export const renderValidArray = <T extends Record<string, any>>(key: string, data: T): React.ReactElement | null => {
  const arrayData = data?.[key as keyof T] as Array<any> | undefined;

  if (!arrayData?.length) return null;
  if (!isValidArray(arrayData)) return null;

  //@ts-ignore
  let pluralLabel = GENERIC_CONSTANTS.getPluralWord[key] || `${key}`;

  return (
    <GridContainer cols={1} className="pt-2">
      <GridItem fullWidth className="pt-5 pb-2">
        <CustomLabel
          label={(translatePropToLabel(pluralLabel)).toUpperCase()}
          colorClass="bg-white"
        />
      </GridItem>

      <GridItem fullWidth>
        <GridContainer cols={2}>
          {arrayData.map((genericArray, index) => (
            <GridItem key={`${key}-column-${index}`} cols={1}>
              <div className="mb-4">
                <CustomLabel
                  label={`${translatePropToLabel(genericArray.label || key).toUpperCase()} ${index + 1}`}
                  colorClass="bg-border"
                  icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                />
                <div className="pl-5 pt-4">
                  {Object.entries(genericArray.value).filter(([fieldKey]) => fieldKey !== REPORT_LABELS.label_default_key).map(([fieldKey, fieldValue]) => (
                    <GridItem
                      key={`${key}-${index}-${fieldKey}`}
                      cols={1}
                      className="py-2"
                    >
                      <ReadOnlyInputField
                        label={`${(getFieldLabel(fieldKey, fieldValue)).toUpperCase()}`}
                        value={String(getFieldValue(fieldValue) || "")}
                        icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                      />
                    </GridItem>
                  ))}
                </div>
              </div>
            </GridItem>
          ))}
        </GridContainer>
      </GridItem>
    </GridContainer>
  );
};

/**
 * Generic function to render arrays of ValueWithSource objects with simple value objects (single property)
 * @param key The key of the array in the data object
 * @param data The data object containing the array
 * @param valueKey The key of the single property in the value object (e.g., "phone number", "email address")
 * @returns React element or null if no data
 */
export const renderSimpleArray = <T extends Record<string, any>>(
  key: string,
  data: T,
  valueKey: string
): React.ReactElement | null => {
  const arrayData = data?.[key as keyof T] as Array<any> | undefined;

  if (!arrayData?.length) return null;
  if (!isValidArray(arrayData)) return null;

  //@ts-ignore
  let pluralLabel = GENERIC_CONSTANTS.getPluralWord[key] || `${key}`;

  return (
    <GridContainer cols={1} className="">
      <GridItem fullWidth className="pt-5">
        <GridContainer cols={1}>
          <div className="mb-4">
            <CustomLabel
              label={pluralLabel.toUpperCase()}
              colorClass="bg-white"
            />
            <GridContainer cols={2} className="pt-2">
              {arrayData.map((item, index) => (
                <GridItem key={`${key}-item-${index}`} cols={1}>
                  <div className="">
                    <GridItem
                      key={`${key}-${index}-${valueKey}`}
                      cols={1}
                      className="py-2"
                    >
                      <ReadOnlyInputField
                        label={`${(item.label || translatePropToLabel(valueKey)).toUpperCase()} ${index + 1}`}
                        value={String(getFieldValue(item.value[valueKey]) || "")}
                        icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                      />
                    </GridItem>
                  </div>
                </GridItem>
              ))}
            </GridContainer>
          </div>
        </GridContainer>
      </GridItem>
    </GridContainer>
  );
};

export const renderValidObject = <T extends Record<string, any>>(
  key: string,
  data: T,
  valueKey: string = 'value'
): React.ReactElement | null => {
  const objectData = data?.[key as keyof T] as Record<string, any> | undefined;

  if (!objectData || !objectData[valueKey]) return null;

  return (
    <GridContainer cols={1} className="pt-2">
      <GridItem fullWidth className="pt-5">
        <GridContainer cols={1}>
          <div className="mb-4">
            <CustomLabel
              label={translatePropToLabel(key).toUpperCase()}
              colorClass="bg-white"
            />
            <GridContainer cols={2} className="pl-5 pt-4">
              {Object.entries(objectData[valueKey]).filter(([fieldKey]) => fieldKey !== "label default key").map(([fieldKey, fieldValue]) => (
                <GridItem
                  key={`${key}-${fieldKey}`}
                  cols={1}
                  className="py-2"
                >
                  <ReadOnlyInputField
                    label={`${(getFieldLabel(fieldKey, fieldValue)).toUpperCase()}`}
                    value={String(getFieldValue(fieldValue) || "")}
                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                  />
                </GridItem>
              ))}
            </GridContainer>
          </div>
        </GridContainer>
      </GridItem>
    </GridContainer>);
};
