import { ValueWithSource } from "./ValueWithSource";

interface _VinculoEmpregaticio {
  "empresa pagadora": string;
  cnpj: string;
  "data admissao": string;
  valor: string;
  "razao social"?: string;
  "label default key"?: string;
  situacao?: string;
  cargo?: string;
  "data demissao"?: string;
}

export interface VinculoEmpregaticio {
  empresa_pagadora: ValueWithSource<string>;
  detalhes: Partial<Record<keyof Omit<_VinculoEmpregaticio, "empresa pagadora">, ValueWithSource<string>>>;
}