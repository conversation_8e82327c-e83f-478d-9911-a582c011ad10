import { ValueWithSource } from "./ValueWithSource";

interface _ServicoPublicoBase {
    "full name": string;
    carreira?: string;
    instituicao?: string;
    "quadro funcional"?: string;
    municipio?: string;
    "label default key"?: string;
}

interface _ServicoPublicoPR extends _ServicoPublicoBase {
    funcao?: string;
}

export type _ServicoPublico = _ServicoPublicoBase | _ServicoPublicoPR;

export interface ServicoPublico {
    servidor: ValueWithSource<string>;
    detalhes: Record<string, ValueWithSource<string>>;
}
