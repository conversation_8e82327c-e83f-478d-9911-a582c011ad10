import { ContatoSalvo } from "../model/ContatosSalvos";

export const contatosSalvosMock: ContatoSalvo = {
    detalhes: [
        {
            "alias": {
                "value": "Aversa Mp Csi Cibernetica",
                "label": "Nome",
                "source": ["IRBIS"],
                "is_deleted": false
            },
            "detalhes": {
                "origin": {
                    "value": "getcontact",
                    "label": "Origem",
                    "source": ["IRBIS"],
                    "is_deleted": false
                }
            }
        },
        {
            "alias": {
                "value": "João Aversa Mprj",
                "label": "Nome",
                "source": ["IRBIS"],
                "is_deleted": false
            },
            "detalhes": {
                "origin": {
                    "value": "tc",
                    "label": "Origem",
                    "source": ["IRBIS"],
                    "is_deleted": false
                }
            }
        },
        {
            "alias": {
                "value": "João <PERSON>",
                "label": "Nome",
                "source": ["IRBIS"],
                "is_deleted": false
            },
            "detalhes": {
                "origin": {
                    "value": "drupe",
                    "label": "Origem",
                    "source": ["IRBIS"],
                    "is_deleted": false
                }
            }
        },
        {
            "alias": {
                "value": "jo<PERSON> aversa",
                "label": "Nome",
                "source": ["IRBIS"],
                "is_deleted": false
            },
            "detalhes": {
                "origin": {
                    "value": "callapp",
                    "label": "Origem",
                    "source": ["IRBIS"],
                    "is_deleted": false
                }
            }
        }
    ]
}

