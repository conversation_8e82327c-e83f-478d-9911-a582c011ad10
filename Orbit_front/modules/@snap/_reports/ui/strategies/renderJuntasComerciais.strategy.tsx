import React from "react";
import { JuntaComercial } from "../../model/JuntasComerciais";
import { RenderStrategy } from "./RenderStrategy";
import { Input, ReadOnlyInputField, CustomLabel } from "@snap/design-system";
import { GridItem } from "../components/GridItem";
import { CustomGridContainer } from "../components/CustomGridContainer";
import { parseValue, translatePropToLabel } from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { isValidArray, renderValidArray, renderSimpleArray } from "./helpers.strategy";

export class RenderJuntasComerciais implements RenderStrategy<JuntaComercial> {

    validateKeys = (keys: Array<keyof JuntaComercial>): boolean => {
        return keys.some((campo) => {
            // Check if the key is in formatByKey
            return campo in this.formatByKey;
        });
    };

    formatByKey: Record<
        string,
        (juntaComercial?: JuntaComercial) => React.ReactElement | null
    > = {
            razao_social: (juntaComercial?: JuntaComercial) => {
                if (!juntaComercial?.razao_social) return null;

                return (
                    <CustomGridContainer cols={3}>
                        <GridItem cols={1} className="mb-6">
                            <div className="text-accent">
                                <CustomLabel
                                    label={(juntaComercial.razao_social.label || "Razão Social").toUpperCase()}
                                    colorClass="bg-primary"
                                />
                            </div>
                            <Input
                                onFocus={(e) => e.target.select()}
                                value={String(juntaComercial.razao_social.value || "")}
                                readOnly
                                className="rounded-none border-0 w-full border-none p-0 text-lg"
                            />
                        </GridItem>
                    </CustomGridContainer>
                );
            },

            detalhes: (juntaComercial?: JuntaComercial) => {
                if (!juntaComercial?.detalhes) return null;

                return (
                    <CustomGridContainer cols={2} className="">
                        {Object.entries(juntaComercial.detalhes).map(([key, value]) => (
                            <GridItem key={key} cols={1} className="">
                                <ReadOnlyInputField
                                    label={`${(translatePropToLabel(value.label || key)).toUpperCase()}`}
                                    value={parseValue(String(value.value))}
                                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                                />
                            </GridItem>
                        ))}
                    </CustomGridContainer>
                );
            },
        }

    render = (juntaComercial: JuntaComercial): React.ReactElement[] => {
        const keys = Object.keys(juntaComercial) as Array<keyof JuntaComercial>;

        if (!this.validateKeys(keys)) {
             console.warn('Invalid keys in data:', keys);
            //throw new Error("Chaves inválidas");
        }

        // Define the order of rendering
        const orderedKeys: Array<keyof JuntaComercial> = [
            'razao_social',
            'detalhes',
        ];

        // Filter the keys to only include those that exist in the juntaComercial object
        const filteredKeys = orderedKeys.filter(key => keys.includes(key));

        return filteredKeys
            .map((chave) => {
                // If the key is in formatByKey, use that formatter
                if (chave in this.formatByKey) {
                    return this.formatByKey[chave]?.(juntaComercial);
                }

                // Check if the value is an array with the expected structure
                const value = juntaComercial[chave];
                if (isValidArray(value)) {
                    return renderValidArray(chave as string, juntaComercial);
                }

                // Otherwise return null
                return null;
            })
            .filter((el): el is React.ReactElement => el !== null);
    };
}