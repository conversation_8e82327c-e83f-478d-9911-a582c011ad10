// Test file to demonstrate phone formatting functionality
// This can be run in the browser console or used for testing

import { formatPhone } from './index.jsx';

// Test cases for Brazilian phone number formatting
const testCases = [
  // International format with country code
  { input: '5511987654321', expected: '+55 (11) 98765-4321', description: 'International mobile' },
  { input: '551134567890', expected: '+55 (11) 3456-7890', description: 'International landline' },
  
  // National format with area code
  { input: '11987654321', expected: '(11) 98765-4321', description: 'Mobile with area code' },
  { input: '1134567890', expected: '(11) 3456-7890', description: 'Landline with area code' },
  
  // Local format without area code
  { input: '987654321', expected: '98765-4321', description: 'Mobile without area code' },
  { input: '34567890', expected: '3456-7890', description: 'Landline without area code' },
  
  // Edge cases
  { input: '', expected: '', description: 'Empty string' },
  { input: null, expected: '', description: 'Null value' },
  { input: undefined, expected: '', description: 'Undefined value' },
  { input: '123', expected: '123', description: 'Too short number' },
  { input: '(11) 98765-4321', expected: '(11) 98765-4321', description: 'Already formatted mobile' },
  { input: '+55 11 98765-4321', expected: '+55 (11) 98765-4321', description: 'International with spaces' },
];

// Function to run tests (for demonstration purposes)
export function runPhoneFormatTests() {
  console.log('🧪 Running Phone Format Tests...\n');
  
  let passed = 0;
  let failed = 0;
  
  testCases.forEach((testCase, index) => {
    const result = formatPhone(testCase.input);
    const success = result === testCase.expected;
    
    if (success) {
      passed++;
      console.log(`✅ Test ${index + 1}: ${testCase.description}`);
      console.log(`   Input: "${testCase.input}" → Output: "${result}"`);
    } else {
      failed++;
      console.log(`❌ Test ${index + 1}: ${testCase.description}`);
      console.log(`   Input: "${testCase.input}"`);
      console.log(`   Expected: "${testCase.expected}"`);
      console.log(`   Got: "${result}"`);
    }
    console.log('');
  });
  
  console.log(`📊 Test Results: ${passed} passed, ${failed} failed`);
  return { passed, failed, total: testCases.length };
}

// Example usage:
// runPhoneFormatTests();
