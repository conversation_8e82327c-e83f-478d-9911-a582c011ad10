import { clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { TOKEN_STORAGE } from "@/services/token.storage";
import { translateWord } from "@domain/helpers/word-dictionary";
import { format, isValid } from "date-fns";
import { ptBR } from "date-fns/locale";

export const removeStoredUser = () => {
  localStorage.removeItem("auth");
  TOKEN_STORAGE.removeToken();
};

export const logout = () => {
  const token = TOKEN_STORAGE.getToken();
  if (token) {
    removeStoredUser();
    window.location.href = "/login";
  }
};

export const getInitials = (name) => {
  if (!name) return "N/A";

  const nameParts = name.trim().split(" ").filter(Boolean);

  if (nameParts.length === 1) {
    return nameParts[0][0].toUpperCase();
  } else if (nameParts.length > 1) {
    const firstInitial = nameParts[0][0];
    const lastInitial = nameParts[nameParts.length - 1][0];
    return `${firstInitial}${lastInitial}`.toUpperCase();
  }

  return "N/A";
};

export const formatDate = (date) => {
  const parsedDate = new Date(date);
  if (isValid(parsedDate)) {
    return format(parsedDate, "dd/MM/yyyy HH:mm", { locale: ptBR });
  }

  return date;
};

/**
 * Verifica se o objeto fornecido está vazio.
 *
 * @param {Object} args - O objeto a ser verificado.
 * @returns {boolean} - Retorna `true` se o objeto estiver vazio, `false` caso contrário.
 */
export const isEmptyObject = (args) => {
  return JSON.stringify(args) === "{}";
};

/**
 * Formats a Date object to Brazilian date format (DD/MM/YYYY).
 * @param {Date} date - The date to format.
 * @returns {string} - The formatted date.
 */
export const formatDateToBrazilian = (date) => {
  if (date instanceof Date)
    return new Intl.DateTimeFormat("pt-BR").format(date);
};

/**
 * Parses a key string by splitting it on underscores or spaces, 
 * translating each word to its corresponding label using the `translateWord` dictionary, 
 * and joining the words back into a single string.
 *
 * @param {string} key - The input key string to be parsed and translated.
 *                      Example: "cep_ou_zipcode" or "cep ou zipcode".
 *
 * @returns {string} - The translated and formatted label string.
 *                     Words are translated based on the `translateWord` dictionary.
 *                     If a word is not found in the dictionary, it is left as-is.
 *                     Example: "CEP ou Código Postal".
 *
 * @example
 * // Example usage:
 * const label = parseKeyToLabel("cep_ou_zipcode");
 * console.log(label); // Output: "CEP ou Código Postal"
 */
export const parseKeyToLabel = (key) => {
  const capitalize = (word) => word.charAt(0).toUpperCase() + word.slice(1);
  // Check if the entire key matches in the translation dictionary
  if (translateWord[key.toLowerCase()]) {
    return capitalize(translateWord[key.toLowerCase()]);
  }

  // If no direct match, split the key and translate word by word
  return key
    .split(/_| /) // Handles both underscores and spaces
    .map((word) => capitalize(translateWord[word.toLowerCase()] || word))
    .join(" ");
};

/**
 * Parses the keys of an object, translating them to corresponding labels using `parseKeyToLabel`.
 *
 * @param {Object} inputObject - The object whose keys need to be parsed and translated.
 * @returns {Object|null} - A new object with translated keys, or `null` if the input is not an object.
 */
export const parseObjectKeys = (inputObject) => {
  if (typeof inputObject !== 'object' || !inputObject || Array.isArray(inputObject)) {
    return;
  }

  const parsedObject = {};
  for (const key in inputObject) {
    if (Object.hasOwnProperty.call(inputObject, key)) {
      const translatedKey = parseKeyToLabel(key);
      parsedObject[translatedKey] = inputObject[key];
    }
  }

  return parsedObject;
};

export const translatePropToLabel = (propName) => {
  if (!propName || typeof propName !== "string") return String(propName);

  const normalizedInput = propName.toLowerCase();

  // Create a lowercase map of keys for case-insensitive matching
  for (const key in TRANSLATED_LABELS) {
    if (key.toLowerCase() === normalizedInput) {
      return TRANSLATED_LABELS[key];
    }
  }

  // Return original string if no match found
  return propName;
}

export function formatPhone(phone) {
  if (!phone) return "";

  // Convert to string in case it's a number
  const phoneStr = String(phone);
  const REMOVE_NON_DIGITS_REGEX = /\D/g;
  const digits = phoneStr.replace(REMOVE_NON_DIGITS_REGEX, "");

  // Handle Brazilian phone number formats
  if (digits.length === 13 && digits.startsWith("55")) {
    // +55 XX XXXXX-XXXX (international format with country code)
    return digits.replace(/(\d{2})(\d{2})(\d{5})(\d{4})/, "+$1 ($2) $3-$4");
  } else if (digits.length === 11) {
    // (XX) XXXXX-XXXX (mobile with area code)
    return digits.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
  } else if (digits.length === 10) {
    // (XX) XXXX-XXXX (landline with area code)
    return digits.replace(/(\d{2})(\d{4})(\d{4})/, "($1) $2-$3");
  } else if (digits.length === 9) {
    // XXXXX-XXXX (mobile without area code)
    return digits.replace(/(\d{5})(\d{4})/, "$1-$2");
  } else if (digits.length === 8) {
    // XXXX-XXXX (landline without area code)
    return digits.replace(/(\d{4})(\d{4})/, "$1-$2");
  }

  // If no pattern matches, return original phone
  return phoneStr;
}

export function cn(...inputs) {
  return twMerge(clsx(inputs))
}
