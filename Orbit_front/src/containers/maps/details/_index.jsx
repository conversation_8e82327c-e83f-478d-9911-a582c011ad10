import React, { useEffect, useRef, useState } from "react";
import VectorLayer from "ol/layer/Vector";
import VectorSource from "ol/source/Vector";
import Feature from "ol/Feature";
import Point from "ol/geom/Point";
import { Map, Overlay, View } from "ol";
import { fromLonLat } from "ol/proj";
import GeoJSON from "ol/format/GeoJSON";
import { Fill, Icon, Stroke, Style, Text } from "ol/style";
import { Cluster, OSM } from "ol/source";
import TileLayer from "ol/layer/Tile";
import { transformExtent } from "ol/proj";
import CircleStyle from "ol/style/Circle";
import { boundingExtent } from "ol/extent";
import { Box, Tooltip, Typography } from "@mui/material";
import "ol/ol.css";
import EventDetailsModal from "../EventDetailsModal";
import LoadingSpinner from "@/components/LoadingSpinner";

const defaultStyle = new Style({
  image: new Icon({
    src: "/default-pin.svg",
    scale: 0.9,
  }),
});

const clusterDistance = 50;
const clusterStyle = (feature) => {
  const size = feature.get("features").length;
  if (size > 1) {
    const baseRadius = 15;
    const maxRadius = 50;
    const radius = Math.min(baseRadius + Math.log(size) * 5, maxRadius);
    return new Style({
      image: new CircleStyle({
        radius: radius,
        fill: new Fill({ color: [254, 71, 60, 0.7] }),
      }),
      text: new Text({
        text: size.toString(),
        scale: 1.5,
        fill: new Fill({ color: "#fff" }),
        stroke: new Stroke({ color: [254, 71, 60, 1], width: 2 }),
      }),
    });
  } else {
    return defaultStyle;
  }
};

const MapDetails = ({ mapData, onFeatureClick }) => {
  const mapContainerRef = useRef(null);
  const mapRef = useRef(null);
  const overlayRef = useRef(null);
  const [tooltipInfo, setTooltipInfo] = useState({ text: "", position: null });
  const brazilBounds = transform([-74.09056, -35.46552, -27.67249, 5.522895]);
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedFeature, setSelectedFeature] = useState(null);

  /* LAYERS */
  const countriesBase = new VectorLayer({
    source: new VectorSource({
      format: new GeoJSON(),
      url: "/countries.json",
    }),
    style: new Style({
      fill: new Fill({
        color: "rgba(217,217,217,0.6)",
      }),
      stroke: new Stroke({
        color: "rgba(217,217,217,0.6)",
      }),
    }),
  });

  countriesBase.set("isPermanent", true); // adicionada essa prop para que a layer não seja removida com a renderização dos pins filtrados

  const brazilLayer = new TileLayer({
    source: new OSM(),
    extent: brazilBounds,
  });

  function transform(extent) {
    return transformExtent(extent, "EPSG:4326", "EPSG:3857");
  }

  function identifyLogType(log) {
    const values = log?.values_ || {};
    const geometry = values?.geometry || {};

    // Check for Cluster Point
    if (Array.isArray(values.features) && values.features.length >= 1) {
      return "Point";
    }

    // Check for Map Area
    if (values.name && geometry.extent_ && geometry.extent_.length === 4) {
      return "Map Area";
    }

    return "Unknown Log Type";
  }

  const addMarkersToMap = (map, mapData) => {
    // remover layers antes de add nova por causa dos filtros
    const layersToRemove = [];
    map.getLayers().forEach((layer) => {
      if (layer instanceof VectorLayer && !layer.get("isPermanent")) {
        layersToRemove.push(layer);
      }
    });

    layersToRemove.forEach((layer) => map.removeLayer(layer));

    const vectorSource = new VectorSource();
    Object.entries(mapData).forEach(([key, events]) => {
      const [lat, lon] = key.split(",").map(Number);
      const feature = new Feature({
        geometry: new Point(fromLonLat([lon, lat])),
        name: key,
        events,
      });
      feature.setStyle(defaultStyle);
      vectorSource.addFeature(feature);
    });

    const clusterSource = new Cluster({
      distance: clusterDistance,
      source: vectorSource,
    });

    const clusterLayer = new VectorLayer({
      source: clusterSource,
      style: (feature) => clusterStyle(feature),
    });

    clusterLayer.setZIndex(10);

    map.addLayer(clusterLayer);

    map.on("click", async (e) => {
      clusterLayer.getFeatures(e.pixel).then((clickedFeatures) => {
        if (clickedFeatures.length) {
          const features = clickedFeatures[0].get("features");
          /* Caso tenha mais de 1 feature (point) dar zoom no cluster */
          if (features.length > 1) {
            const extent = boundingExtent(
              features.map((r) => r.getGeometry().getCoordinates())
            );
            map
              .getView()
              .fit(extent, { duration: 1000, padding: [50, 50, 50, 50] });
          }
        }
      });

      const feature = map.getFeaturesAtPixel(e.pixel)[0];
      const isSinglePoint =
        feature.get("features") && feature.get("features").length === 1;

      if (!feature) {
        return;
      }

      if (!isSinglePoint) {
        setSelectedFeature(null);
        setModalOpen(false);
        return;
      }

      const coordinates = feature.values_.features[0].values_.name;
      await onFeatureClick(coordinates);

      setSelectedFeature(feature);
      setModalOpen(true);
    });

    map.on("pointermove", (event) => {
      const featuresAtPixel = map.getFeaturesAtPixel(event.pixel)[0];
      const features = featuresAtPixel?.get("features");
      const isSinglePoint = !!(features && features.length === 1);

      if (!featuresAtPixel) {
        return;
      }

      if (features) {
        isSinglePoint &&
          setTooltipInfo({
            text: `${features[0]?.get("name")} (Eventos: ${
              features[0]?.get("events")?.length
            })`,
            position: event.pixel,
          });
      } else {
        setTooltipInfo({ text: "", position: null });
      }

      if (identifyLogType(featuresAtPixel) === "Point") {
        map.getViewport().style.cursor = "pointer";
        return;
      }

      map.getViewport().style.cursor = "crosshair";
    });
  };

  useEffect(() => {
    const map = new Map({
      target: mapContainerRef.current,
      layers: [countriesBase, brazilLayer],
      view: new View({
        center: [-5332530.72, -1769808.58], // [ -5332530.72, -1769808.58 ] Chapada dos Guimarães, no estado de Mato Grosso
        zoom: 2,
        minZoom: 4,
        smoothExtentConstraint: true,
      }),
    });

    const overlay = new Overlay({
      element: document.createElement("div"),
    });
    map.addOverlay(overlay);
    overlayRef.current = overlay;
    mapRef.current = map;

    return () => {
      map.setTarget(null);
    };
  }, []);

  useEffect(() => {
    if (mapData && mapRef.current) {
      addMarkersToMap(mapRef.current, mapData);
    }
  }, [mapData]);

  const handleCloseModal = () => {
    setModalOpen(false);
    setSelectedFeature(null);
  };

  return (
    <div style={{ position: "relative", height: "100vh" }}>
      <div ref={mapContainerRef} style={{ width: "100%", height: "100%" }} />
      {tooltipInfo.text && tooltipInfo.position && (
        <Tooltip
          title={<Typography>{tooltipInfo.text}</Typography>}
          open
          placement="top"
          arrow
          style={{
            position: "absolute",
            left: tooltipInfo.position[0],
            top: tooltipInfo.position[1] - 10,
            pointerEvents: "none",
          }}
          slotProps={{
            tooltip: {
              sx: {
                bgColor: "background.default",
              },
            },
          }}
        >
          <div />
        </Tooltip>
      )}

      <EventDetailsModal
        modalOpen={modalOpen}
        handleCloseModal={handleCloseModal}
        selectedFeature={selectedFeature}
      />
    </div>
  );
};

export default MapDetails;
