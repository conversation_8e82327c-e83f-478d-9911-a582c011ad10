import { Grid } from "@mui/material";
import useAppStore from "@/store/useAppStore.jsx";
import { caseStatusList, accentLabelStyle } from "@/utils/constants.jsx";
import * as S from "@/containers/cases/form/styles.js";
import Box from "@mui/material/Box";
import { DefaultBtn } from "@/components/buttons/index.jsx";
import { SaveAlt } from "@mui/icons-material";
import { CasesGateway } from "@/services/gateways/cases.gateway.js";
import { useTheme } from "@mui/material/styles";
import PropTypes from "prop-types";
import { useCasesCRUD } from "@/hooks/useCasesCRUD.js";
import { useAlert } from "@/hooks/useAlert.jsx";
import { useNavigate, useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import FullScreenLoader from "@/components/loader";
import { useCaseById } from "@/hooks/useCaseById.js";
import { useQueryClient } from "@tanstack/react-query";
import labels from "@constants/casos/casos.js";
import {Separator, Select, Text, CustomLabel, Input, Textarea} from '@snap/design-system'
import AutoChipInput from "@/components/inputs/AutoChipInput.jsx";
import CustomTimeline from "@/containers/cases/CustomTimeline.jsx";

export const VisaoGeral = ({ initialCaseState, mode }) => {
  const queryClient = useQueryClient();
  const [caseState, setCaseState] = useState({});
  const updateStateCallback = (keyPair) => {
    setCaseState({
      ...caseState,
      ...keyPair,
    });
  };
  const showAlert = useAlert();
  const navigate = useNavigate();
  const authState = useAppStore.use.authState();
  const { id } = useParams();
  const theme = useTheme();
  const { useListTypesOfCrime, useListBonds } = useCasesCRUD();
  const { useUpdateCaseGeneral, useAddTypeOfCrime, useDeleteTypeOfCrime } =
    useCaseById(id);
  const { data: bonds } = useListBonds();
  const { data: typesOfCrime } = useListTypesOfCrime();

  const isSaveDisabled = undefined;
  // !caseState?.nome || !caseState?.numero || !caseState?.status;

  const { mutateAsync: updateCase } = useUpdateCaseGeneral();
  const { mutateAsync: addTypeOfCrime } = useAddTypeOfCrime();
  const { mutateAsync: deleteTypeOfCrime } = useDeleteTypeOfCrime();

  const handleSave = async () => {
    if (mode === "update") {
      console.log("case update");
      console.log(caseState);
      try {
        await updateCase(caseState);

        await queryClient.invalidateQueries({
          queryKey: ["cases"],
          exact: true,
        });
      } catch (e) {
        console.error(e);
      }
      return;
    }
    if (!caseState.nome) {
      alert("É necessário adicionar um nome ao caso");
      return;
    }
    const [bond] = bonds.usuarios.collection;

    try {
      const newCase = await CasesGateway.createCase(caseState);
      const updatedCase = await CasesGateway.createBond({
        id_caso: newCase.id_caso,
        persons: [
          {
            id: authState.sub,
            bond: bond.id,
          },
        ],
      });
      if (caseState.tipos_de_delito && caseState.tipos_de_delito.length > 0) {
        const itemsIds = caseState.tipos_de_delito.map((item) => item.id);
        await CasesGateway.addTypesOfCrime({
          id_caso: newCase.id_caso,
          typesOfCrime: itemsIds,
        });
      }
      console.log("newCase: ", newCase);
      console.log("updatedCase -> ", updatedCase);
      setCaseState({ ...newCase });
      showAlert("Caso salvo com sucesso!", "success");
      navigate(`/caso/${newCase.id_caso}`, { replace: true });
    } catch (error) {
      console.error(error);
      showAlert("Erro ao tentar salvar novo caso", "error");
    }
  };

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setCaseState({
      ...caseState,
      [name]: value,
    });
  };

  const handlePropertiesChange = (event) => {
    const { name, value } = event.target;
    console.log(name, value)
    setCaseState({
      ...caseState,
      properties: {
        ...caseState.properties,
        [name]: value,
      },
    });
  };

  const handleChangeTypesOfCrime = async (items, syncCallback) => {
    if (caseState.id_caso && !!items && items.length > 0) {
      await addTypeOfCrime(items);
      // syncCallback(items);
    } else {
      syncCallback(items);
    }
  };

  const handleAsyncDeleteTypeOfCrime = async (item, syncCallback) => {
    if (caseState.id_caso && !!item && "id" in item) {
      deleteTypeOfCrime(item);
      // syncCallback(item);
    } else {
      syncCallback(item);
    }
  };

  useEffect(() => {
    if (initialCaseState) {
      setCaseState(initialCaseState);
    }
  }, [initialCaseState]);

  return (
    <div style={{
      display: 'flex',
      width: '100%',
    }}>
      <FullScreenLoader isLoading={!initialCaseState && mode === "update"} />
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        width: '50%',
        gap: '16px',
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          width: '100%',
          gap: '16px',
        }}>
          <div className="space-y-1 w-full">
            <CustomLabel label="NOME DA INVESTIGAÇÃO:"/>
            <Input
              name="nome"
              required
              value={caseState?.nome || ""}
              onChange={handleInputChange}
            />
          </div>
          <div className="space-y-1 w-full">
            <CustomLabel label="NÚMERO DO CASO:"/>
            <Input
              name="numero"
              required
              value={caseState?.numero || ""}
              onChange={handleInputChange}
            />
          </div>
        </div>
        <Grid container direction={"row"} size={12}>

          <div>
            <CustomLabel label={"Status do Caso"}/>
            <Select
              name={"status"}
              value={caseState.status || ''}
              options={caseStatusList}
              onChange={(e) => handleInputChange({target:{name: "status", value: e}})}
            />
          </div>
    
        </Grid>
        <Grid size={{ sm: 12 }}>
          <CustomLabel label={labels.modus_operandi}/>
          <Textarea
            name="modus_operandi"
            value={caseState?.properties?.modus_operandi || ""}
            onChange={handlePropertiesChange}
          />
        </Grid>
        <Grid size={{ sm: 12 }}>
          <CustomLabel label={"MOTIVAÇÕES"}/>
          <Textarea
            name="motivacoes"
            value={caseState?.properties?.motivacoes}
            onChange={handlePropertiesChange}
          />
        </Grid>
        <Grid size={{ sm: 12 }}>
          <CustomLabel label={"TIPO DE DELITO:"} />
          <AutoChipInput
            customLabelProp="nome"
            options={typesOfCrime}
            valueList={caseState?.tipos_de_delito}
            label={""}
            placeholder={labels.selecione_tipos_delito}
            stateName={"tipos_de_delito"}
            setList={updateStateCallback}
            asyncDeleteHandler={handleAsyncDeleteTypeOfCrime}
            asyncChangeHandler={handleChangeTypesOfCrime}
            slotProps={{
              inputLabel: {
                shrink: true,
                style: accentLabelStyle,
              },
            }}
          />
        </Grid>
      </div>
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        width: '50%',
      }}
      >
        <CustomTimeline />
      </div>
      <Box sx={S.StyledButtonConteiner}>
        <DefaultBtn
          text={"Salvar"}
          iconStart={<SaveAlt />}
          sx={{ backgroundColor: theme.palette.info.dark }}
          onClick={handleSave}
          disabled={isSaveDisabled}
        />
      </Box>
    </div>
  );
};

VisaoGeral.propTypes = {
  mode: PropTypes.string.isRequired,
  initialCaseState: PropTypes.object,
};
