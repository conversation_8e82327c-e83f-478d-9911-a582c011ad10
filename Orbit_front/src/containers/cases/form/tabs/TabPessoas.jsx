import {useState} from "react";
import { SearchInput } from "@/components/inputs/index.jsx";
import ListGrid from "@/components/ListGrid.jsx";
import {
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
} from "@mui/material";
import AddPersonsDialog from "../../AddPersonsDialog.jsx";
import { Controller, useForm } from "react-hook-form";
import PersonSubjectCard from "../../PersonSubjectCard.jsx";
import {useCaseById} from "@/hooks/useCaseById.js";
import {useCasesCRUD} from "@/hooks/useCasesCRUD.js";
import {useParams} from "react-router-dom";
import CustomToolbar from "@/components/CustomToolbar";
import {AnimatedFilledButton, Button, Icon} from "@snap/design-system";

export const Pessoas = () => {
  const {id} = useParams()
  const {
    useListBonds
  } = useCasesCRUD()
  const {useGetCase} = useCaseById(id)
  const {data: caseState, isLoading, dataUpdatedAt} = useGetCase()
  const {data: bonds} = useListBonds()
  const { control } = useForm();
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredData, setFilteredData] = useState([]);
  const [open, setOpen] = useState(false);
  const [vinculoFilter, setVinculoFilter] = useState("");

  const handleVinculoChange = (event) => {
    const value = event.target.value;
    setVinculoFilter(value);
  };

  const handleOpen = (event) => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleSearch = (query) => {
    setSearchQuery(query);

    if (query.trim() === "") {
      setFilteredData(caseState?.people);
    } else {
      const lowercasedQuery = query.toLowerCase();
      const filteredResults = caseState?.people?.filter(
        (target) => {
          return Object.values(target).some((value) =>
            value.toString().toLowerCase().includes(lowercasedQuery)
          );
        }
      );
      setFilteredData(filteredResults);
    }
  };

  const AddButtonComponent = () => {
    return <AnimatedFilledButton icon={<Icon src={"/icons/icone_bigplus.svg"}/>}>
      <Button
        variant="outline"
        onClick={handleOpen}
      >
        Adicionar Pessoa de Interesse
      </Button>
    </AnimatedFilledButton>;
  };


  return (
    <Grid container spacing={2} direction={"column"}>
      <CustomToolbar>
        <CustomToolbar.SearchInput value={searchQuery} onChange={(value) => handleSearch(value)}/>
        <Grid size={{ xs: 12, md: 4 }}>
          <FormControl variant="filled" fullWidth>
            <InputLabel variant="standard">Tipo de Vínculo</InputLabel>
            <Controller
              control={control}
              name={"relationship"}
              defaultValue={""}
              render={({ field }) => (
                <Select
                  variant="standard"
                  {...field}
                  onChange={(event) => {
                    field.onChange(event);
                    handleVinculoChange(event);
                  }}
                >
                  <MenuItem key={0} value={''} disabled>
                    Selecione...
                  </MenuItem>
                  {bonds?.pessoas?.collection?.map((option) => (
                    <MenuItem key={option.id} value={option.id}>
                      {option.nome}
                    </MenuItem>
                  ))}
                </Select>
              )}
            />
          </FormControl>
        </Grid>
        <Grid
          container
          size={{ xs: 12, md: 4 }}
          alignItems={"center"}
          justifyContent={"flex-end"}
        >
          <CustomToolbar.SortIcon/>
          <CustomToolbar.EventIcon/>
          <CustomToolbar.ListIcon/>
        </Grid>
      </CustomToolbar>

      <ListGrid
        list={
          caseState?.pessoas?.collection|| []
        }
        cardComponent={PersonSubjectCard}
        AddButtonComponent={AddButtonComponent}
        sequential={true}
        isLoading={isLoading}
      />
      <AddPersonsDialog
        open={open}
        handleClose={handleClose}
      />
    </Grid>
  );
};
