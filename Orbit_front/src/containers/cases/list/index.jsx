import {Grid} from "@mui/material";
import {useState} from "react";
import {useNavigate} from "react-router-dom";
import {usePermissionManager} from "@/services/permissionManager.jsx";
import CustomToolbar from "@/components/CustomToolbar.jsx";
import CaseCard from "@/containers/cases/CaseCard.tsx";
import ListGrid from "@/components/ListGrid.jsx";
import {useTheme} from "@mui/material/styles";
import {useCasesCRUD} from "@/hooks/useCasesCRUD.js";
import {useAlert} from "@/hooks/useAlert.jsx";
import UpdatedAtLabel from "@/components/updated-at-label/index.jsx";
import {AnimatedFilledButton, Button, Icon} from "@snap/design-system";

const ListCases = () => {
  const theme = useTheme()
  const showAlert = useAlert()
  const {useListCases, invalidateCasesList} = useCasesCRUD();
  const {canCreateCases} = usePermissionManager();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredData, setFilteredData] = useState([]);

  const {isLoading, fetchStatus, data, dataUpdatedAt} = useListCases()

  const updateCasesList = async () => {
    showAlert("Atualizando dados", "info");
    invalidateCasesList().then(() => showAlert("Atualizado com sucesso", "success"))
  }

  const handleSearch = (query) => {
    setSearchQuery(query);

    if (query.trim() === "") {
      setFilteredData(data);
    } else {
      const lowercasedQuery = query.toLowerCase();
      const filteredResults = data?.filter((item) => {
        return Object.values(item).some((item) =>
          item.toString().toLowerCase().includes(lowercasedQuery)
        );
      });
      setFilteredData(filteredResults);
    }
  };

  const handleAddNewCase = () => {
    navigate("/novo-caso");
  };

  const AddButtonComponent = () => {
    return <AnimatedFilledButton icon={<Icon src={"/icons/icone_bigplus.svg"}/>}>
      <Button
        variant="outline"
        onClick={canCreateCases() ? handleAddNewCase : alert('Voce nao tem permissao')}
      >
        Novo Caso
      </Button>
    </AnimatedFilledButton>;
  };

  return (
    <>
      <CustomToolbar>
        <CustomToolbar.SearchInput
          value={searchQuery}
          onChange={handleSearch}
        />
        <CustomToolbar.RefreshButton onClick={updateCasesList} isDisabled={(isLoading || fetchStatus !== 'idle')}/>
        <Grid
          container
          size={{xs: 12, md: 4}}
          alignItems={"center"}
          justifyContent={"flex-end"}
        >
          <CustomToolbar.SortIcon/>
          <CustomToolbar.EventIcon/>
          <CustomToolbar.ListIcon/>
        </Grid>
      </CustomToolbar>
      <UpdatedAtLabel timestamp={dataUpdatedAt} fetchStatus={fetchStatus}/>
      <ListGrid
        list={searchQuery ? filteredData : (data || [])}
        cardComponent={CaseCard}
        sequential
        // AddButtonComponent={canCreateCases() ? AddButtonComponent : null}
        AddButtonComponent={AddButtonComponent}
      />
    </>
  );
};

export default ListCases;
