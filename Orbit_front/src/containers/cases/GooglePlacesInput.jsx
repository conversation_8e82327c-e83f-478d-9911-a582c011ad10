import React, { useEffect, useMemo, useRef, useState } from "react";
import Box from "@mui/material/Box";
import TextField from "@mui/material/TextField";
import Autocomplete from "@mui/material/Autocomplete";
import LocationOnIcon from "@mui/icons-material/LocationOn";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import parse from "autosuggest-highlight/parse";
import { debounce } from "@mui/material/utils";
import { DefaultBtn } from "@/components/buttons/index.jsx";
import { Check } from "@mui/icons-material";

const MAPS_API = import.meta.env.VITE_GOOGLE_MAPS_API;

function loadScript(src, position, id) {
  if (!position) return;

  const script = document.createElement("script");
  script.setAttribute("async", "");
  script.setAttribute("id", id);
  script.src = src;
  position.appendChild(script);
}

const autocompleteService = { current: null };
const placesService = { current: null };

const GooglePlacesInput = ({ onSelectAddress }) => {
  const [value, setValue] = useState(null);
  const [inputValue, setInputValue] = useState("");
  const [options, setOptions] = useState([]);
  const loaded = useRef(false);

  useEffect(() => {
    if (typeof window !== "undefined" && !loaded.current) {
      if (!document.querySelector("#google-maps")) {
        loadScript(
          `https://maps.googleapis.com/maps/api/js?key=${MAPS_API}&libraries=places&language=pt-BR`,
          document.querySelector("head"),
          "google-maps"
        );
      }
      loaded.current = true;
    }
  }, []);

  const fetch = useMemo(
    () =>
      debounce((request, callback) => {
        autocompleteService.current.getPlacePredictions(request, callback);
      }, 400),
    []
  );

  useEffect(() => {
    let active = true;

    if (!autocompleteService.current && window.google) {
      autocompleteService.current =
        new window.google.maps.places.AutocompleteService();
    }
    if (!autocompleteService.current) return;

    if (inputValue === "") {
      setOptions(value ? [value] : []);
      return;
    }

    fetch(
      {
        input: inputValue,
        componentRestrictions: { country: ["br"] },
        language: "pt-BR",
      },
      (results) => {
        if (active) {
          let newOptions = [];
          if (value) newOptions = [value];
          if (results) newOptions = [...newOptions, ...results];
          setOptions(newOptions);
        }
      }
    );

    return () => {
      active = false;
    };
  }, [value, inputValue, fetch]);

  const handleAdd = async () => {
    if (!value) return;

    // Initialize PlacesService
    if (!placesService.current && window.google) {
      const mapDiv = document.createElement("div");
      placesService.current = new window.google.maps.places.PlacesService(
        mapDiv
      );
    }

    const request = {
      placeId: value.place_id,
      fields: ["address_component", "geometry"],
    };

    placesService.current.getDetails(request, (place, status) => {
      if (
        status === window.google.maps.places.PlacesServiceStatus.OK &&
        place
      ) {
        const address = mapPlaceDetailsToAddress(place);
        onSelectAddress(address);
        console.log("address: ", address);
      }
    });
  };

  const mapPlaceDetailsToAddress = (place) => {
    const components = place.address_components || [];
    const findComponent = (type) =>
      components.find((c) => c.types.includes(type))?.long_name || "";

    console.log("place.address_components: ", place.address_components);

    return {
      cep: findComponent("postal_code"),
      logradouro: findComponent("route"),
      numero: findComponent("street_number"),
      bairro:
        findComponent("sublocality") || findComponent("sublocality_level_1"),
      cidade: findComponent("administrative_area_level_2"),
      estado: findComponent("administrative_area_level_1"),
      pais: findComponent("country"),
      complemento: "",
      latitude: place.geometry?.location?.lat() || "",
      longitude: place.geometry?.location?.lng() || "",
    };
  };

  return (
    <Box display={"flex"} width={"100%"} gap={2}>
      <Autocomplete
        fullWidth
        getOptionLabel={(option) =>
          typeof option === "string" ? option : option.description
        }
        filterOptions={(x) => x}
        options={options}
        autoComplete
        includeInputInList
        filterSelectedOptions
        value={value}
        noOptionsText="Sem Local"
        onChange={(event, newValue) => {
          setValue(newValue);
          setInputValue("");
          setOptions([]);
        }}
        onInputChange={(event, newInputValue) => setInputValue(newInputValue)}
        renderInput={(params) => (
          <TextField {...params} label="Buscar uma Localização" fullWidth />
        )}
        renderOption={(props, option) => {
          const matches =
            option.structured_formatting.main_text_matched_substrings || [];
          const parts = parse(
            option.structured_formatting.main_text,
            matches.map((match) => [match.offset, match.offset + match.length])
          );

          return (
            <li {...props}>
              <Grid container sx={{ alignItems: "center" }}>
                <Grid sx={{ display: "flex", width: 44 }}>
                  <LocationOnIcon sx={{ color: "text.secondary" }} />
                </Grid>
                <Grid
                  sx={{ width: "calc(100% - 44px)", wordWrap: "break-word" }}
                >
                  {parts.map((part, index) => (
                    <Box
                      key={index}
                      component="span"
                      sx={{ fontWeight: part.highlight ? "bold" : "regular" }}
                    >
                      {part.text}
                    </Box>
                  ))}
                  <Typography variant="body2" sx={{ color: "text.secondary" }}>
                    {option.structured_formatting.secondary_text}
                  </Typography>
                </Grid>
              </Grid>
            </li>
          );
        }}
      />
      <DefaultBtn
        iconStart={<Check />}
        text={"Adicionar"}
        onClick={handleAdd}
        sx={{ width: "fit-content" }}
      />
    </Box>
  );
};

export default GooglePlacesInput;
