import { <PERSON><PERSON> } from "@/components/ui/button.js";
import React, { useLayoutEffect, useState, useEffect } from "react";
import zc from "@dvsl/zoomcharts";
import "@dvsl/zoomcharts/lib/assets/zc.css";
import "../../zoomcharts-overwrite.css";
import { useGraphManagement } from "@/hooks/useGraphManagementCRUD.jsx";
import { useParams, useSearchParams } from "react-router-dom";
import useAppStore from "@/store/useAppStore.jsx";
import { DateRangePicker } from "@/components/DateRangePicker";
import { DateRange } from "react-day-picker";
import SearchHistoryList from "./SearchHistoryList";
import { saveSearchFilter } from "@/services/searchHistory.service";
import { useAlert } from "@/hooks/useAlert";
import useSearchHistoryStore from "@/store/useSearchHistoryStore";
import { DataTable, Column } from "@/components/Table"
import { formatPhone } from "@/utils"
import { cn } from "@/components/ui/utils"

const node_colors_settings = {
  fillColor: "#3A454B",
  lineColor: "#f44336",
  shadowBlur: 0,
  shadowColor: "255,255,255,0.01",
};

//Should render a table with all calls and messages related to the selected node
// Resume the link from / to and the date range of the call/message
// Rows containing the detail of the call/message
interface ICallAndMessageTableParams {
  isVisibleCondition: boolean;
  event_id: string;
  data: {
    phones: Array<{ id: string; label: string }>;
    dateRange: { from: Date; to: Date };
    details: {
      from: { id: string; label: string };
      to: { id: string; label: string };
      date: string;
      duration: number
    }
  };
}

const CallsAndMessagesTableDetails = ({ source }: { source: any }) => {
  const currentSelectedLink = useAppStore.use.currentSelectedLink();
  if (!currentSelectedLink) return null;

  const [
    event_type,
    _phone_one,
    _phone_two,
  ] = currentSelectedLink.id.split(":");

  const phone_one = source.phones[_phone_one].label;
  const phone_two = source.phones[_phone_two].label;

  const label = event_type === "mensagem" ? "Mensagens" : "Chamadas";
  const event_detail_key = event_type === "mensagem" ? "acc_messages" : "acc_calls";

  // Get the events data
  const eventsData = source[event_detail_key]["acc_data"][currentSelectedLink.id] || [];

  // Define columns for the DataTable
  const columns: Column[] = [
    {
      key: "date",
      header: "Data",
      render: (value: string) => new Date(value).toLocaleString("pt-BR")
    },
    {
      key: "sender",
      header: "Remetente",
      render: (value: string, row: any) => {
        // Format phone number if it looks like a phone number
        const phoneNumber = row.senderPhone || value;
        return phoneNumber ? formatPhone(phoneNumber) : value;
      }
    },
    {
      key: "receiver",
      header: "Destinatário",
      render: (value: string, row: any) => {
        // Format phone number if it looks like a phone number
        const phoneNumber = row.receiverPhone || value;
        return phoneNumber ? formatPhone(phoneNumber) : value;
      }
    },
    {
      key: "duration",
      header: "Duração",
      render: (value: any, row: any) => {
        // For calls, show duration if available, for messages show message count or timestamp
        if (event_type === "chamada" && row.details?.duration) {
          return `${row.details.duration}s`;
        }
        return event_type === "mensagem" ? "N/A" : new Date(row.details?.timestamp).toLocaleString("pt-BR");
      }
    }
  ];

  // Transform data to match DataTable expected format
  const tableData = eventsData.map((event: any) => ({
    id: event.id,
    date: event.details?.timestamp,
    sender: event.from?.label,
    receiver: event.to?.label,
    senderPhone: phone_one, // Store phone number for formatting
    receiverPhone: phone_two, // Store phone number for formatting
    duration: event.details?.duration,
    details: event.details // Keep original details for custom render functions
  }));

  console.log(source);

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
    >
      <div
        className="bg-background border border-border rounded-lg shadow-lg max-w-4xl w-full max-h-[90vh] mx-4 flex flex-col"
      >
        <div className="flex justify-between items-center p-4 border-b border-border">
          <h2 className="text-lg font-semibold text-foreground">
            {label} entre {formatPhone(phone_one)} e {formatPhone(phone_two)}
          </h2>
          <div className="flex items-center gap-2">
            {
              source.dateRange?.from && source.dateRange?.to && (
                <span className="text-sm text-muted-foreground">
                  {source.dateRange.from.toLocaleString()} - {source.dateRange.to.toLocaleString()}
                </span>
              )
            }
            <Button variant="link" onClick={() => useAppStore.getState().clearCurrentSelectedLink()}>
              Fechar
            </Button>
          </div>
        </div>
        <div className="flex-1 overflow-y-auto p-4">
          <DataTable
            columns={columns}
            data={tableData}
            keyField="id"
            className="h-full"
          />
        </div>
      </div>
    </div>
  );
};

const ZoomCharts = ({ useMock = false }) => {
  const setCurrentSelectedNode = useAppStore.use.setCurrentSelectedNode();
  const setCurrentSelectedLink = useAppStore.use.setCurrentSelectedLink();
  const currentSelectedLink = useAppStore.use.currentSelectedLink();
  const clearSelectedLink = useAppStore.use.clearCurrentSelectedLink();
  const { useGetGraph } = useGraphManagement();
  const { id: caseId } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const toast = useAlert();

  // Get date range from URL params if they exist
  const initialDateRange = (() => {
    const filterBy = searchParams.get("filter_by");
    const fromParam = searchParams.get("from");
    const toParam = searchParams.get("to");

    if (filterBy === "period" && fromParam && toParam) {
      return {
        from: new Date(fromParam),
        to: new Date(toParam),
      };
    }
    return undefined;
  })();

  const [currentDate, setCurrentDate] = useState<DateRange | undefined>(initialDateRange as DateRange);
  const [forceUpdate, setForceUpdate] = useState(false);
  const [isFilterSaved, setIsFilterSaved] = useState(false);

  // Get checkFilterExists from the store
  const checkFilterExists = useSearchHistoryStore(state => state.checkFilterExists);

  // Check if current filter is already saved
  useEffect(() => {
    const checkIfFilterSaved = async () => {
      if (currentDate?.from && currentDate?.to) {
        const filter = {
          filter_by: "period",
          from: currentDate.from.toISOString(),
          to: currentDate.to.toISOString(),
        };
        const exists = await checkFilterExists(filter);
        setIsFilterSaved(exists);
      } else {
        setIsFilterSaved(false);
      }
    };

    checkIfFilterSaved();
  }, [currentDate, checkFilterExists]);

  // Pass searchParams to useGetGraph
  const { data, isLoading }: { data: any; isLoading: boolean } = useGetGraph(caseId, searchParams);

  function handleClearMapFilter() {
    setCurrentDate(undefined);

    // Clear URL search params related to filtering
    searchParams.delete("filter_by");
    searchParams.delete("from");
    searchParams.delete("to");
    setSearchParams(searchParams, { replace: true });

    setForceUpdate(!forceUpdate);
  }

  function handleApplyFilter() {
    if (currentDate?.from && currentDate?.to) {
      // Update URL search params with filter values
      searchParams.set("filter_by", "period");
      searchParams.set("from", currentDate.from.toISOString());
      searchParams.set("to", currentDate.to.toISOString());
    } else {
      // If no date range, clear filter params
      searchParams.delete("filter_by");
      searchParams.delete("from");
      searchParams.delete("to");
    }

    // Update URL without navigation
    setSearchParams(searchParams, { replace: true });
    setForceUpdate(!forceUpdate);
  }

  // Get addSearchFilter from the store
  const addSearchFilter = useSearchHistoryStore(state => state.addSearchFilter);

  // Save current filter to search history
  async function handleSaveFilter() {
    if (currentDate?.from && currentDate?.to) {
      try {
        const filter = {
          filter_by: "period",
          from: currentDate.from.toISOString(),
          to: currentDate.to.toISOString(),
        };

        // Use the service function which uses the store internally
        const result = await saveSearchFilter(filter);

        // Show feedback toast
        toast(result.success ? "Filtro salvo com sucesso." : "Houve um problema ao salvar o filtro.", result.success ? "success" : "error");

        // No need to manually update isFilterSaved as the useEffect will handle it
      } catch (error) {
        console.error("Erro ao salvar filtro:", error);
        toast("Não foi possível salvar o filtro", "error");
      }
    }
  }

  // Load a filter from search history
  function handleLoadFilter(filter: { filter_by: string; from: string; to: string }) {
    if (filter.filter_by === "period" && filter.from && filter.to) {
      setCurrentDate({
        from: new Date(filter.from),
        to: new Date(filter.to),
      });

      // Update URL search params
      searchParams.set("filter_by", filter.filter_by);
      searchParams.set("from", filter.from);
      searchParams.set("to", filter.to);
      setSearchParams(searchParams, { replace: true });

      setForceUpdate(!forceUpdate);
    }
  }

  const imageCroppingDecorator = (imageUrl: string) => {
    if (imageUrl.includes("icons")) {
      return "fit";
    }
    return "crop";
  };

  const imageSrcHandler = (imageUrl: string) => {
    const defaultReturn = "/icons/default.svg";
    if (typeof imageUrl === "undefined") return defaultReturn;
    if (imageUrl === "/default.svg") return defaultReturn;

    return imageUrl;
  };

  function nodeStyle(node: any) {
    node.aura = node.data.aura;
    node.label = node.data.label;
    node.image = imageSrcHandler(node.data.image ?? undefined);
    node.loaded = true;
    const nodeImage = node.data.image ?? "/icons/icone_default.svg";
    node.imageCropping = imageCroppingDecorator(nodeImage);

    if (node.data.isExpandable) {
      node.items = [
        {
          px: 0,
          py: -1,
          text: "+",
          backgroundStyle: { fillColor: "orange" },
        },
      ];
    } else {
      node.items = [];
    }

    if ((node.hovered || node.selected) && node.data.description && node.data.description !== "Nao possui") {
      node.items = [
        {
          px: 0,
          py: 1,
          text: node.data.description,
          fillColor: "transparent",
          textStyle: {
            font: "20px",
          },
        },

      ];
    }
  }

  function linkStyle(link: any) {
    link.label = link.data.label;
    link.fromDecoration = null;
    link.toDecoration = "open arrow";
    if (link.hovered) {
      link.radius = 4;
    }
    if (link.selected) {
      link.radius = 4;
      link.fillColor = "#f44336";
    }
  }

  function onNodeClick(event: any) {
    const clickNodeProps = event.clickNode;
    console.log(
      "%cClick Node Properties:",
      "background: #222; color: #bada55; font-weight: bold; padding: 4px; margin: 2px;",
    );
    console.log(clickNodeProps);
    setCurrentSelectedNode(clickNodeProps);
  }

  function onLinkClick(event: any) {
    const clickLinkProps = event.clickLink;
    const event_key = clickLinkProps.data.id.split(":").at(0);
    if (!["chamada", "mensagem"].includes(event_key)) return;
    console.log(
      "%cClick Link Properties:",
      "background: #222; color: #bada55; font-weight: bold; padding: 4px; margin: 2px;",
    );
    console.log(clickLinkProps.data);
    setCurrentSelectedLink(clickLinkProps.data);
  }

  function onGraphClick(event: any) {
    event.preventDefault();
    if (!event.clickNode) {
      setCurrentSelectedNode(null);
    }

    if (!event.clickLink) {
      setCurrentSelectedLink(null);
    }

    if (event.clickNode) {
      onNodeClick(event);
    } else if (event.clickLink) {
      onLinkClick(event);
    }
  }

  // Node filtering is now handled by the filter.js script before data reaches the component

  useLayoutEffect(() => {
    if (!data) return;
    //@ts-ignore
    const { graphData, details } = data;
    const { nodes, links, auras } = graphData as { nodes: any[], links: any[], auras: any };
    if (nodes.length < 0 || links.length < 0) return;
    new zc.NetChart({
      assetsUrlBase: "https://cdn.zoomcharts.com/1/latest/",
      container: document.getElementById("netChart") as HTMLElement,
      navigation: {
        mode: "showall",
        initialNodes: [nodes[0].id],
      },
      events: {
        onClick: onGraphClick,
      },
      // Filters are now applied before data reaches the component
      style: {
        
        item: {
          backgroundStyle: {
            fillColor: "transparent",
          },
        },
        dragSelection: {
          fillColor: "transparent",
        },
        link: {
          fillColor: "#ffffff",
          cursor: "pointer",
          radius: 2,
        },
        linkLabel: {
          rotateWithLink: true,
          padding: 2,
          backgroundStyle: {
            fillColor: "#000000",
            lineColor: "#f7f7f7",
          },
          textStyle: {
            fillColor: "white",
          },
        },
        node: {
          cursor: "pointer",
          radius: 40,
          fillColor: "transparent",
          fillGradient: undefined,
          lineColor: "#3A454B",
          lineWidth: 5,
        },
        nodeBackground: node_colors_settings,
        nodeHovered: node_colors_settings,
        nodeSelected: node_colors_settings,
        nodeFocused: node_colors_settings,
        nodeLabel: {
          padding: 2,
          margin: 4,
          borderRadius: 2,
          textStyle: {
            fillColor: "white",
          },
          backgroundStyle: {
            fillColor: "transparent",
            lineColor: "transparent",
          },
        },
        nodeStyleFunction: nodeStyle,
        linkStyleFunction: linkStyle,
      },
      auras: {
        cellSize: 10,
        intensity: 10,
        overlap: true,
        enabled: true,
        defaultStyle: {
          showInLegend: true,
          shadowBlur: 35,
        },
        style: auras,
      },
      toolbar: {
        fullscreen: true,
        enabled: true,
      },
      interaction: {
        resizing: {
          enabled: false,
        },
      },
      data: {
        //@ts-ignore Cobertura do Zc ao TS eh pessima
        preloaded: { nodes, links },
      },
      layout: {
        mode: "radial",
        nodeSpacing: 99, // horizontal spacing between nodes
        rowSpacing: 70, //
      },
      legend: {
        enabled: true,
        padding: 6,
        marker: { size: 22 },
        maxLineSymbols: 12,
        panel: {
          side: "top",
          align: "center",
          location: "outside",
          margin: 10,
        },
      },
      theme: zc.NetChart.themes.dark,
    });

  }, [forceUpdate, data]);

  return (
    <div className="chart-wrapper relative bg-background">
      {
        currentSelectedLink && (
          <CallsAndMessagesTableDetails source={{
            ...data?.details,
            dateRange: currentDate,
          }} />
        )
      }
      <div className="absolute top-0 left-0 z-10 p-4 bg-background/80 backdrop-blur-sm border border-border rounded-md shadow-sm">
        <div className="flex flex-col gap-2">
          <DateRangePicker selected={currentDate} onSelect={(range) => setCurrentDate(range)} />
          <div className="flex gap-2 items-center">
            <Button onClick={handleApplyFilter}>
              Aplicar
            </Button>
            <Button variant="link" onClick={handleClearMapFilter}>
              Limpar
            </Button>
            <Button
              variant="outline"
              onClick={handleSaveFilter}
              disabled={!currentDate?.from || !currentDate?.to || isFilterSaved}
              title={isFilterSaved ? "Este filtro já está salvo" : ""}
            >
              Salvar Busca
            </Button>
          </div>
          <SearchHistoryList onSelectFilter={handleLoadFilter} />
        </div>
      </div>
      {
        isLoading &&
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <span className="text-base font-medium text-foreground">Carregando...</span>
        </div>
      }
      <div
        id="netChart"
        className="chart"
        style={{
          height: "calc(100vh - 76px)", //compensar altura do header do VisualizationLayout
          backgroundColor: "transparent",
        }}
      ></div>
    </div>
  );
};

export default ZoomCharts;
