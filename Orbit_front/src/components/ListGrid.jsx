import {createElement} from "react";
import PropTypes from "prop-types";
import {<PERSON><PERSON><PERSON><PERSON><PERSON>,CgPlayListRemove} from "react-icons/cg";
import {StandardList, Text} from "@snap/design-system";

const ListGrid = ({
                    list = [],
                    cardComponent,
                    emptyListMessage = "lista vazia",
                    AddButtonComponent,
                    otherCardProps = {},
                    close = {}, //fechar modal com a lista
                    isLoading = false
                  }) => {
  if (isLoading) {
    return (
      <CgSpinner
        style={{
          animation: 'rotate 2s linear infinite'
        }}
        color="disabled"
        fontSize="large"
      />
    );
  }

  // Prepare the content elements
  const contentElements = [];

  // Add the AddButtonComponent if provided
  if (AddButtonComponent) {
    contentElements.push(<AddButtonComponent key="add-button"/>);
  }

  // Handle empty list or add list items
  if (!list?.length && !isLoading) {
    contentElements.push(
      <div key="empty-list">
        <CgPlayListRemove color="disabled" fontSize="large"/>
        <Text variant="body-lg">{emptyListMessage}</Text>
      </div>
    );
  } else {
    // Add each list item individually to the array
    list.forEach((item, index) => {
      contentElements.push(
        createElement(cardComponent, {item, ...otherCardProps, ...close, key: `item-${index}`})
      );
    });
  }

  return (
    <StandardList masonry>
      {contentElements}
    </StandardList>
  );
};

ListGrid.propTypes = {
  list: PropTypes.array.isRequired,
  cardComponent: PropTypes.elementType.isRequired,
  isLoading: PropTypes.bool.isRequired,
};

export default ListGrid;
