"use client";

import { Table, TableBody, <PERSON><PERSON>ell, TableHead, TableHeader, TableRow } from "./ui/table";
import { Checkbox } from "./ui/checkbox";
import { useState } from "react";
import type { ReactNode } from "react";
import { Text, Button } from "@snap/design-system";
import { cn } from "@/components/ui/utils";

export interface Column<T = any> {
  key: string;
  header?: string;
  render?: (value: any, row: T, index: number) => ReactNode;
  className?: string;
  hidden?: boolean;
}

export interface ActionButton<T = any> {
  icon: ReactNode;
  onClick: (row: T) => void;
  label?: string;
  className?: string;
}

export interface DataTableProps<T = any> {
  columns: Column<T>[];
  data: T[];
  className?: string;
  showHeaders?: boolean;
  actionsTitle?: string;
  actions?: ActionButton<T>[];
  enableSelection?: boolean;
  onSelectionChange?: (selectedRows: T[]) => void;
  keyField?: keyof T;
  pagination?: {
    pageSize: number
    totalItems?: number
    currentPage?: number
    onPageChange?: (page: number) => void
  };
}

const columnColors = ["bg-table-cell", "bg-table-cell-alt"];
const headerColors = ["bg-table-header", "bg-table-header-alt"];

export function DataTable<T extends Record<string, any> = any>({
                                                                 columns,
                                                                 data,
                                                                 className = "",
                                                                 showHeaders = true,
                                                                 actionsTitle = "Ações",
                                                                 actions = [],
                                                                 enableSelection = false,
                                                                 onSelectionChange,
                                                                 keyField = "id" as keyof T,
                                                                 pagination,
                                                               }: DataTableProps<T>) {
  const [selectedRows, setSelectedRows] = useState<Record<string | number, boolean>>({});
  const [currentPage, setCurrentPage] = useState(pagination?.currentPage || 1);
  // paginação
  const pageSize = pagination?.pageSize || data.length;
  const totalPages = pagination ? Math.ceil((pagination.totalItems || data.length) / pageSize) : 1;
  const paginatedData = pagination ? data.slice((currentPage - 1) * pageSize, currentPage * pageSize) : data;

  const handleSelectRow = (row: T, isSelected: boolean) => {
    const rowKey = String(row[keyField] || "");
    const newSelectedRows = { ...selectedRows, [rowKey]: isSelected };
    setSelectedRows(newSelectedRows);

    if (onSelectionChange) {
      const selectedItems = data.filter((item) => {
        const itemKey = String(item[keyField] || "");
        return newSelectedRows[itemKey];
      });
      onSelectionChange(selectedItems);
    }
  };

  const handleSelectAll = (isSelected: boolean) => {
    const newSelectedRows: Record<string | number, boolean> = {};
    paginatedData.forEach((row) => {
      const rowKey = String(row[keyField] || "");
      newSelectedRows[rowKey] = isSelected;
    });
    setSelectedRows(newSelectedRows);

    if (onSelectionChange) {
      onSelectionChange(isSelected ? [...paginatedData] : []);
    }
  };

  const isRowSelected = (row: T) => {
    const rowKey = String(row[keyField] || "");
    return !!selectedRows[rowKey];
  };

  const areAllRowsSelected = () => {
    return paginatedData.length > 0 && paginatedData.every((row) => isRowSelected(row));
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    if (pagination?.onPageChange) {
      pagination.onPageChange(page);
    }
  };

  const visibleColumns = columns.filter((col) => !col.hidden);

  return (
    <div className="space-y-4">
      <div className={`w-full border border-table-border ${className}`}>
        <Table>
          {showHeaders && (
            <TableHeader>
              <TableRow className="border-table-border hover:bg-table-hover">
                {enableSelection && (
                  <TableHead className={cn("w-12", headerColors[0])}>
                    <Checkbox checked={areAllRowsSelected()} onCheckedChange={handleSelectAll} className="ml-3" />
                  </TableHead>
                )}

                {visibleColumns.map(
                  (column, index) =>
                    column.header && (
                      <TableHead
                        key={column.key}
                        className={cn(
                          "p-3.5",
                          headerColors[index % headerColors.length],
                          column.className,
                        )}
                      >
                        <Text variant="body-lg" className="font-semibold">{column.header}</Text>
                      </TableHead>
                    ),
                )}

                {actions.length > 0 && (
                  <TableHead
                    className={cn("text-center", headerColors[visibleColumns.length % headerColors.length])}
                  ><Text variant="body-lg" className="font-semibold">{actionsTitle}</Text></TableHead>
                )}
              </TableRow>
            </TableHeader>
          )}

          <TableBody>
            {paginatedData.map((row, rowIndex) => (
              <TableRow key={String(row[keyField]) || rowIndex} className="border-table-border hover:bg-table-hover">
                {enableSelection && (
                  <TableCell className={cn("w-12", columnColors[0])}>
                    <Checkbox
                      checked={isRowSelected(row)}
                      onCheckedChange={(checked) => handleSelectRow(row, !!checked)}
                      className="ml-3 cursor-pointer"
                    />
                  </TableCell>
                )}

                {visibleColumns.map((column, colIndex) => (
                  <TableCell
                    key={`${rowIndex}-${column.key}`}
                    className={cn(
                      "p-3.5",
                      columnColors[colIndex % columnColors.length],
                      column.className,
                    )}
                  >
                    {column.render ? column.render(row[column.key as keyof T], row, rowIndex) : row[column.key as keyof T]}
                  </TableCell>
                ))}

                {actions.length > 0 && (
                  <TableCell className={cn("w-24", columnColors[visibleColumns.length % columnColors.length])}>
                    <div className="flex justify-end">
                      {actions.map((action, actionIndex) => {
                        return (
                          <>
                            <Button
                              key={actionIndex}
                              variant="ghost"
                              size="sm"
                              onClick={() => action.onClick(row)}
                              className={cn("hover:bg-transparent", action.className)}
                              title={action.label}
                            >
                              {action.icon}

                            </Button>
                            {
                              actionIndex !== actions.length - 1 && (
                                <div className="w-px h-4 mx-2 my-auto border-l border-dotted border-foreground" />
                              )
                            }
                          </>
                        );
                      })}
                    </div>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

    </div>
  );
}
