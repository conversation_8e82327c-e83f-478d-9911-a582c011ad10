lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@dvsl/zoomcharts':
        specifier: 1.21.4
        version: 1.21.4
      '@emotion/react':
        specifier: ^11.14.0
        version: 11.14.0(@types/react@19.1.5)(react@19.1.0)
      '@emotion/styled':
        specifier: ^11.14.0
        version: 11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0)
      '@grafana/faro-web-sdk':
        specifier: ^1.13.3
        version: 1.18.2
      '@grafana/faro-web-tracing':
        specifier: ^1.13.3
        version: 1.18.2
      '@mui/icons-material':
        specifier: ^7.1.0
        version: 7.1.0(@mui/material@7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(@types/react@19.1.5)(react@19.1.0)
      '@mui/lab':
        specifier: ^7.0.0-beta.12
        version: 7.0.0-beta.12(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@mui/material@7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@mui/material':
        specifier: ^7.1.0
        version: 7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@mui/utils':
        specifier: ^7.1.0
        version: 7.1.0(@types/react@19.1.5)(react@19.1.0)
      '@mui/x-date-pickers':
        specifier: ^8.3.0
        version: 8.4.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@mui/material@7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(@mui/system@7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(date-fns@4.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-accordion':
        specifier: ^1.2.11
        version: 1.2.11(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-checkbox':
        specifier: ^1.3.2
        version: 1.3.2(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-dropdown-menu':
        specifier: ^2.1.15
        version: 2.1.15(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-popover':
        specifier: ^1.1.14
        version: 1.1.14(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot':
        specifier: ^1.2.3
        version: 1.2.3(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-tooltip':
        specifier: ^1.2.7
        version: 1.2.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@snap/design-system':
        specifier: ^1.2.4
        version: 1.2.4(@emotion/is-prop-valid@1.3.1)(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(immer@10.1.1)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(tailwindcss@4.1.7)(use-sync-external-store@1.5.0(react@19.1.0))
      '@tailwindcss/vite':
        specifier: ^4.0.6
        version: 4.1.7(vite@6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0))
      '@tanstack/react-query':
        specifier: ^5.61.3
        version: 5.76.1(react@19.1.0)
      '@tsparticles/engine':
        specifier: ^3.7.1
        version: 3.8.1
      '@tsparticles/react':
        specifier: ^3.0.0
        version: 3.0.0(@tsparticles/engine@3.8.1)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@uppy/aws-s3':
        specifier: ^4.2.1
        version: 4.2.3(@uppy/core@4.4.5)
      '@uppy/core':
        specifier: ^4.4.1
        version: 4.4.5
      '@uppy/dashboard':
        specifier: ^4.3.4
        version: 4.3.4(@uppy/core@4.4.5)
      '@uppy/drag-drop':
        specifier: ^4.1.3
        version: 4.1.3(@uppy/core@4.4.5)
      '@uppy/file-input':
        specifier: ^4.1.3
        version: 4.1.3(@uppy/core@4.4.5)
      '@uppy/locales':
        specifier: ^4.2.0
        version: 4.5.2
      '@uppy/progress-bar':
        specifier: ^4.0.0
        version: 4.2.1(@uppy/core@4.4.5)
      '@uppy/react':
        specifier: ^4.2.1
        version: 4.2.3(@uppy/core@4.4.5)(@uppy/dashboard@4.3.4(@uppy/core@4.4.5))(@uppy/drag-drop@4.1.3(@uppy/core@4.4.5))(@uppy/file-input@4.1.3(@uppy/core@4.4.5))(@uppy/progress-bar@4.2.1(@uppy/core@4.4.5))(@uppy/status-bar@4.1.3(@uppy/core@4.4.5))(react@19.1.0)
      autosuggest-highlight:
        specifier: ^3.3.4
        version: 3.3.4
      axios:
        specifier: ^1.7.7
        version: 1.9.0
      class-variance-authority:
        specifier: ^0.7.1
        version: 0.7.1
      clsx:
        specifier: ^2.1.1
        version: 2.1.1
      date-fns:
        specifier: ^4.1.0
        version: 4.1.0
      date-fns-tz:
        specifier: ^3.2.0
        version: 3.2.0(date-fns@4.1.0)
      framer-motion:
        specifier: ^12.6.2
        version: 12.12.1(@emotion/is-prop-valid@1.3.1)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      immer:
        specifier: ^10.1.1
        version: 10.1.1
      jwt-decode:
        specifier: ^4.0.0
        version: 4.0.0
      lucide-react:
        specifier: ^0.475.0
        version: 0.475.0(react@19.1.0)
      ol:
        specifier: ^10.3.1
        version: 10.5.0
      ol-mapbox-style:
        specifier: ^12.4.0
        version: 12.6.1(ol@10.5.0)
      prop-types:
        specifier: ^15.8.1
        version: 15.8.1
      react:
        specifier: ^19.0.0
        version: 19.1.0
      react-day-picker:
        specifier: 8.10.1
        version: 8.10.1(date-fns@4.1.0)(react@19.1.0)
      react-dom:
        specifier: ^19.0.0
        version: 19.1.0(react@19.1.0)
      react-dropzone:
        specifier: ^14.2.9
        version: 14.3.8(react@19.1.0)
      react-hook-form:
        specifier: ^7.53.0
        version: 7.56.4(react@19.1.0)
      react-icons:
        specifier: ^5.3.0
        version: 5.5.0(react@19.1.0)
      react-router-dom:
        specifier: ^6.26.2
        version: 6.30.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      swiper:
        specifier: ^11.1.14
        version: 11.2.7
      tailwind-merge:
        specifier: ^3.0.1
        version: 3.3.0
      tailwindcss-animate:
        specifier: ^1.0.7
        version: 1.0.7(tailwindcss@4.1.7)
      tsparticles:
        specifier: ^3.7.1
        version: 3.8.1
      zustand:
        specifier: ^5.0.0-rc.2
        version: 5.0.5(@types/react@19.1.5)(immer@10.1.1)(react@19.1.0)(use-sync-external-store@1.5.0(react@19.1.0))
    devDependencies:
      '@commitlint/cli':
        specifier: ^19.7.1
        version: 19.8.1(@types/node@22.15.21)(typescript@5.8.3)
      '@commitlint/config-conventional':
        specifier: ^19.7.1
        version: 19.8.1
      '@eslint/js':
        specifier: ^9.9.0
        version: 9.27.0
      '@playwright/test':
        specifier: ^1.50.1
        version: 1.52.0
      '@tanstack/eslint-plugin-query':
        specifier: ^5.61.3
        version: 5.74.7(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)
      '@tanstack/react-query-devtools':
        specifier: ^5.61.3
        version: 5.76.1(@tanstack/react-query@5.76.1(react@19.1.0))(react@19.1.0)
      '@testing-library/dom':
        specifier: ^10.4.0
        version: 10.4.0
      '@testing-library/jest-dom':
        specifier: ^6.6.3
        version: 6.6.3
      '@testing-library/react':
        specifier: ^16.2.0
        version: 16.3.0(@testing-library/dom@10.4.0)(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@testing-library/user-event':
        specifier: ^14.6.1
        version: 14.6.1(@testing-library/dom@10.4.0)
      '@types/chroma-js':
        specifier: ^3.1.1
        version: 3.1.1
      '@types/react':
        specifier: ^19.0.10
        version: 19.1.5
      '@types/react-dom':
        specifier: ^19.0.4
        version: 19.1.5(@types/react@19.1.5)
      '@typescript-eslint/eslint-plugin':
        specifier: ^8.32.1
        version: 8.32.1(@typescript-eslint/parser@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/parser':
        specifier: ^8.32.1
        version: 8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)
      '@vitejs/plugin-react':
        specifier: ^4.3.4
        version: 4.4.1(vite@6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0))
      autoprefixer:
        specifier: ^10.4.20
        version: 10.4.21(postcss@8.5.3)
      eslint:
        specifier: ^9.9.0
        version: 9.27.0(jiti@2.4.2)
      eslint-config-prettier:
        specifier: ^10.1.5
        version: 10.1.5(eslint@9.27.0(jiti@2.4.2))
      eslint-plugin-import-helpers:
        specifier: ^2.0.1
        version: 2.0.1(eslint@9.27.0(jiti@2.4.2))
      eslint-plugin-prettier:
        specifier: ^5.4.0
        version: 5.4.0(eslint-config-prettier@10.1.5(eslint@9.27.0(jiti@2.4.2)))(eslint@9.27.0(jiti@2.4.2))(prettier@3.5.3)
      eslint-plugin-react:
        specifier: ^7.37.5
        version: 7.37.5(eslint@9.27.0(jiti@2.4.2))
      eslint-plugin-react-hooks:
        specifier: ^5.2.0
        version: 5.2.0(eslint@9.27.0(jiti@2.4.2))
      eslint-plugin-react-refresh:
        specifier: ^0.4.20
        version: 0.4.20(eslint@9.27.0(jiti@2.4.2))
      eslint-plugin-unused-imports:
        specifier: ^4.1.4
        version: 4.1.4(@typescript-eslint/eslint-plugin@8.32.1(@typescript-eslint/parser@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.27.0(jiti@2.4.2))
      global-jsdom:
        specifier: ^26.0.0
        version: 26.0.0(jsdom@26.1.0)
      globals:
        specifier: ^15.15.0
        version: 15.15.0
      husky:
        specifier: ^9.1.7
        version: 9.1.7
      jsdom:
        specifier: ^26.0.0
        version: 26.1.0
      lint-staged:
        specifier: ^15.4.3
        version: 15.5.2
      postcss:
        specifier: ^8.5.2
        version: 8.5.3
      tailwindcss:
        specifier: ^4.0.6
        version: 4.1.7
      typescript:
        specifier: ^5.7.3
        version: 5.8.3
      typescript-eslint:
        specifier: ^8.24.0
        version: 8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)
      vite:
        specifier: ^6.1.0
        version: 6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0)
      vitest:
        specifier: ^3.0.5
        version: 3.1.4(@types/node@22.15.21)(jiti@2.4.2)(jsdom@26.1.0)(lightningcss@1.30.1)(yaml@2.8.0)

packages:

  '@adobe/css-tools@4.4.3':
    resolution: {integrity: sha1-vuu++wJk/esy0wUqyuDg2UMVqaI=}

  '@alloc/quick-lru@5.2.0':
    resolution: {integrity: sha1-e/aLIMCjUPk2kV/K4G9Y4yAHzjA=}
    engines: {node: '>=10'}

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=}
    engines: {node: '>=6.0.0'}

  '@asamuzakjp/css-color@3.2.0':
    resolution: {integrity: sha1-zEL1uFxZP3nx+k8l0rmzIeYdF5Q=}

  '@babel/code-frame@7.27.1':
    resolution: {integrity: sha1-IA9xXmbVKiOyIalDVTSpHME61b4=}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.27.2':
    resolution: {integrity: sha1-QYP55kL9hOdOPup/+pOkEuOxAsk=}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.27.1':
    resolution: {integrity: sha1-id5R6GvRIkYAPjUkcExJVBsWw+Y=}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.27.1':
    resolution: {integrity: sha1-hi1PrYWPcgjt1IfCi1gUQDa3YjA=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.27.2':
    resolution: {integrity: sha1-RqD276uAjVHSnOloWN0Qzocycz0=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.27.1':
    resolution: {integrity: sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.27.1':
    resolution: {integrity: sha1-4WY7i3HS3pSNpcT7KiDKTz7Cem8=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-plugin-utils@7.27.1':
    resolution: {integrity: sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.27.1':
    resolution: {integrity: sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.27.1':
    resolution: {integrity: sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.27.1':
    resolution: {integrity: sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.27.1':
    resolution: {integrity: sha1-/8JwEwOGB826MojmksNhHAahiqQ=}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.27.2':
    resolution: {integrity: sha1-V3UYvtsXos5CEq/QUuAfffCUESc=}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-transform-react-jsx-self@7.27.1':
    resolution: {integrity: sha1-r2eNhQas9SxXfKxz/3/mYVyF/JI=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-source@7.27.1':
    resolution: {integrity: sha1-3P4sJAlLt1e/c5YDdOfFXkNPGfA=}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/runtime@7.27.1':
    resolution: {integrity: sha1-n84xPRLJp3UH8mTedGJuh/0NxUE=}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.27.2':
    resolution: {integrity: sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.27.1':
    resolution: {integrity: sha1-TbdykCsTO73dHE96fuR3YcG58pE=}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.27.1':
    resolution: {integrity: sha1-ne/FPBb8iZ5GlB/GkBqe6hydhWA=}
    engines: {node: '>=6.9.0'}

  '@commitlint/cli@19.8.1':
    resolution: {integrity: sha1-hffZ8zE0Th8KK52LJP02lUZuEVg=}
    engines: {node: '>=v18'}
    hasBin: true

  '@commitlint/config-conventional@19.8.1':
    resolution: {integrity: sha1-6rQt9YzaRPGEEK4MvWeF7OAPIUs=}
    engines: {node: '>=v18'}

  '@commitlint/config-validator@19.8.1':
    resolution: {integrity: sha1-Kem7E2D6QblDmyPY4l3qrwlzBrU=}
    engines: {node: '>=v18'}

  '@commitlint/ensure@19.8.1':
    resolution: {integrity: sha1-k4xU1vWGvaYAtcjo6ELtsoFUbhQ=}
    engines: {node: '>=v18'}

  '@commitlint/execute-rule@19.8.1':
    resolution: {integrity: sha1-UwADY7c3dz4tJel8IPFeqnh0IGc=}
    engines: {node: '>=v18'}

  '@commitlint/format@19.8.1':
    resolution: {integrity: sha1-PgmxKRs+KQkteobwr7vPwNmdOtQ=}
    engines: {node: '>=v18'}

  '@commitlint/is-ignored@19.8.1':
    resolution: {integrity: sha1-/tCFE2DqLSF5nq+Oye9tmMFVNuM=}
    engines: {node: '>=v18'}

  '@commitlint/lint@19.8.1':
    resolution: {integrity: sha1-whv5AAylTkHFsBOcmKrxJHPAO7A=}
    engines: {node: '>=v18'}

  '@commitlint/load@19.8.1':
    resolution: {integrity: sha1-uZex9lqWG/CkcYnxX23IeGzrRXY=}
    engines: {node: '>=v18'}

  '@commitlint/message@19.8.1':
    resolution: {integrity: sha1-1dDYeDdIPZ+bRVn/oG4aqibSZtY=}
    engines: {node: '>=v18'}

  '@commitlint/parse@19.8.1':
    resolution: {integrity: sha1-cxJdBPB/EUd89WPL/gzJ9tyFp0c=}
    engines: {node: '>=v18'}

  '@commitlint/read@19.8.1':
    resolution: {integrity: sha1-gSkw/Q9hbnluEidRy5gzRuVFTsg=}
    engines: {node: '>=v18'}

  '@commitlint/resolve-extends@19.8.1':
    resolution: {integrity: sha1-pEu0wi4+fUB8yaN1j89Y9cNgtpQ=}
    engines: {node: '>=v18'}

  '@commitlint/rules@19.8.1':
    resolution: {integrity: sha1-HOpT1b+XDOVtwQXh2l5mVaL+el8=}
    engines: {node: '>=v18'}

  '@commitlint/to-lines@19.8.1':
    resolution: {integrity: sha1-waKKhFQse6MhwcEReLg64CQle0c=}
    engines: {node: '>=v18'}

  '@commitlint/top-level@19.8.1':
    resolution: {integrity: sha1-LJQhidg6KbIf97pukWBzAe/fWRY=}
    engines: {node: '>=v18'}

  '@commitlint/types@19.8.1':
    resolution: {integrity: sha1-eXH71WsM+zFpKk4ZQbdKyCF8ROU=}
    engines: {node: '>=v18'}

  '@csstools/color-helpers@5.0.2':
    resolution: {integrity: sha1-glksmnwrg8KT2RYYlOKmRx/rl7g=}
    engines: {node: '>=18'}

  '@csstools/css-calc@2.1.3':
    resolution: {integrity: sha1-b2iv/LVpqGuRll6GItZEvjWghCM=}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^3.0.4
      '@csstools/css-tokenizer': ^3.0.3

  '@csstools/css-color-parser@3.0.9':
    resolution: {integrity: sha1-jYG3fW8hFJW1EA7EytTIgo3kn2s=}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-parser-algorithms': ^3.0.4
      '@csstools/css-tokenizer': ^3.0.3

  '@csstools/css-parser-algorithms@3.0.4':
    resolution: {integrity: sha1-dEJuk70cTcqz5EH1zHuk+zXZQ1Y=}
    engines: {node: '>=18'}
    peerDependencies:
      '@csstools/css-tokenizer': ^3.0.3

  '@csstools/css-tokenizer@3.0.3':
    resolution: {integrity: sha1-pVAshTkmX+y9hzweOVqJAznxGcI=}
    engines: {node: '>=18'}

  '@dvsl/zoomcharts@1.21.4':
    resolution: {integrity: sha1-Z7pKedyES0FkDpiOH8n9DuWobAM=}

  '@emotion/babel-plugin@11.13.5':
    resolution: {integrity: sha1-6rjWXb3tdODs/SjcIY51YHxOe8A=}

  '@emotion/cache@11.14.0':
    resolution: {integrity: sha1-7kSyaYbuuTyL6Cu5Lx96myGy7XY=}

  '@emotion/hash@0.9.2':
    resolution: {integrity: sha1-/5IhufWLTf5h5hmneIc0vWP2iYs=}

  '@emotion/is-prop-valid@1.3.1':
    resolution: {integrity: sha1-jVzxEy+DbXrb5CzwtJ33gW/IgkA=}

  '@emotion/memoize@0.9.0':
    resolution: {integrity: sha1-dFlp1kmXd3a0P8dkjFVqqkYrQQI=}

  '@emotion/react@11.14.0':
    resolution: {integrity: sha1-z6rjXrxn3Z706i6azGzSnhV90F0=}
    peerDependencies:
      '@types/react': '*'
      react: '>=16.8.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@emotion/serialize@1.3.3':
    resolution: {integrity: sha1-0pFTEAXxfXBNBGOgMv5nnzdlCeg=}

  '@emotion/sheet@1.4.0':
    resolution: {integrity: sha1-ySmcNNJIvCboJWNzX3iVPS78qDw=}

  '@emotion/styled@11.14.0':
    resolution: {integrity: sha1-9HynIZsaKVGG12YVgzdvzqlfD/M=}
    peerDependencies:
      '@emotion/react': ^11.0.0-rc.0
      '@types/react': '*'
      react: '>=16.8.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@emotion/unitless@0.10.0':
    resolution: {integrity: sha1-KvL3x+UVD0l72r2EjOeyGKJ890U=}

  '@emotion/use-insertion-effect-with-fallbacks@1.2.0':
    resolution: {integrity: sha1-ioy3e1kOCa/7lg9P8emonlMnOL8=}
    peerDependencies:
      react: '>=16.8.0'

  '@emotion/utils@1.4.2':
    resolution: {integrity: sha1-bfbEWIH8scQS1miKMRqYt/WcG1I=}

  '@emotion/weak-memoize@0.4.0':
    resolution: {integrity: sha1-XhP6yIfwjET3awzK8zcOsA/sm7Y=}

  '@esbuild/aix-ppc64@0.25.4':
    resolution: {integrity: sha1-gw1kdsu8oMAFE2rwcwNka0GfEWI=}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [aix]

  '@esbuild/android-arm64@0.25.4':
    resolution: {integrity: sha1-0R1PwpkiTnKeIZDKytvMAOep/Wc=}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.25.4':
    resolution: {integrity: sha1-VmC9JQgFU90qKEOPKkAaKZWb2bE=}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.25.4':
    resolution: {integrity: sha1-GN3ecFv5hOjNnv7FThmawYvHvuE=}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.25.4':
    resolution: {integrity: sha1-sLf7VduPxvXeWgIHrphuucR2bmc=}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.25.4':
    resolution: {integrity: sha1-5oE/3roLujVss1CkuAVD++Zr8m8=}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.25.4':
    resolution: {integrity: sha1-3BGnPTzNwwhWe5CLQ8ZpjoUHWb4=}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.25.4':
    resolution: {integrity: sha1-kdoI24vRv/XzGSTFeoHasm6ToUM=}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.25.4':
    resolution: {integrity: sha1-78FeRclFoIJwj5qfc7+o1NtJcoo=}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.25.4':
    resolution: {integrity: sha1-m5PD5UrEmi7eb5BucF1dkG9tueg=}
    engines: {node: '>=18'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.25.4':
    resolution: {integrity: sha1-vo7yw+HZn8otJcQWspfQA2BiNZY=}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.25.4':
    resolution: {integrity: sha1-sIQKJwfD/ALuwojT+d76OCfNeoc=}
    engines: {node: '>=18'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.25.4':
    resolution: {integrity: sha1-KhmOWkWMnw51iBpOY9JroM+d858=}
    engines: {node: '>=18'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.25.4':
    resolution: {integrity: sha1-ZPSuC5I9fdcvuGC5si7bQgB8+PU=}
    engines: {node: '>=18'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.25.4':
    resolution: {integrity: sha1-+yhEsR/d3TninSkcfPgPmbDVFY0=}
    engines: {node: '>=18'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.25.4':
    resolution: {integrity: sha1-FGaHbgqjVgx2c+Y/3ryCeHB7x1A=}
    engines: {node: '>=18'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.25.4':
    resolution: {integrity: sha1-wQ/eiZRV23y6XxGzvM+g5Bv00M0=}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-arm64@0.25.4':
    resolution: {integrity: sha1-AuSD+8vj8Y8LAmEqlBt3vnbBEaQ=}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [netbsd]

  '@esbuild/netbsd-x64@0.25.4':
    resolution: {integrity: sha1-7EAfsLHtCsAdl4VkxfyGNO0dwu0=}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-arm64@0.25.4':
    resolution: {integrity: sha1-8nLC9Bz+odkbk9SHpRtcXKeoyMQ=}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [openbsd]

  '@esbuild/openbsd-x64@0.25.4':
    resolution: {integrity: sha1-LiWVC8EPqdseXIaOPVDET3wVD9c=}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.25.4':
    resolution: {integrity: sha1-zVlvplpns7etxezVLZ9XM4MuGr0=}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.25.4':
    resolution: {integrity: sha1-tNvLV7Ie6vgzHkJMOZm4nYlR3Ig=}
    engines: {node: '>=18'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.25.4':
    resolution: {integrity: sha1-QQhC5dZtTs4XV2NOKXqHY164L3o=}
    engines: {node: '>=18'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.25.4':
    resolution: {integrity: sha1-CxfsinCyOFgn1SMUwSUxYKC5usw=}
    engines: {node: '>=18'}
    cpu: [x64]
    os: [win32]

  '@eslint-community/eslint-utils@4.7.0':
    resolution: {integrity: sha1-YHCEYwxsAzmSoILebm+8GotSF1o=}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution: {integrity: sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/config-array@0.20.0':
    resolution: {integrity: sha1-ehIy6CN2cS0zQAEqL1YaJ2TRmI8=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/config-helpers@0.2.2':
    resolution: {integrity: sha1-N3n3a4lN46jsR2O3lmDm1U1bEBA=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/core@0.14.0':
    resolution: {integrity: sha1-MmKJOAlo6vfpbzZOHkz4863y0AM=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/eslintrc@3.3.1':
    resolution: {integrity: sha1-5V9/HdQAYA3QZtu6NJxMC6yRaWQ=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/js@9.27.0':
    resolution: {integrity: sha1-GBojRgh3xIT23QOJD04/ov3rj/A=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/object-schema@2.1.6':
    resolution: {integrity: sha1-WDaatbWzyhF4gMD2wLDzL2lQ8k8=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@eslint/plugin-kit@0.3.1':
    resolution: {integrity: sha1-txsDey1NaDlt8EqMNaSUgeVZMGc=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@essentials/memoize-one@1.1.0':
    resolution: {integrity: sha1-mOfUJdFUiSGkwNKHeiv+LVrbhzU=}

  '@essentials/one-key-map@1.2.0':
    resolution: {integrity: sha1-lLtlfEIYojvUCh94Yhr3sq1JRhw=}

  '@essentials/raf@1.2.0':
    resolution: {integrity: sha1-J1VnACFhhDOqQ3RT3JitS8hDYXc=}

  '@essentials/request-timeout@1.3.0':
    resolution: {integrity: sha1-1HnFISYKTvxDww2mrqZT6kB8MMA=}

  '@floating-ui/core@1.7.0':
    resolution: {integrity: sha1-Gv8nqZPqGyVKWGMYwpw7FuoPTQo=}

  '@floating-ui/dom@1.7.0':
    resolution: {integrity: sha1-+fg+5P7nisI62eZbEo/BGieFdTI=}

  '@floating-ui/react-dom@2.1.2':
    resolution: {integrity: sha1-oTSbv2oOXLXe1V0CN2byCk1DmjE=}
    peerDependencies:
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@floating-ui/utils@0.2.9':
    resolution: {integrity: sha1-UN6jYWvIGR+44RIoO0nq/wPnhCk=}

  '@grafana/faro-core@1.18.2':
    resolution: {integrity: sha1-ogfRdCgD3EmeFqLfa2O5Ba/H1f0=}

  '@grafana/faro-web-sdk@1.18.2':
    resolution: {integrity: sha1-GJ7muZCu+3iyoeLUXDCEPdS85cI=}

  '@grafana/faro-web-tracing@1.18.2':
    resolution: {integrity: sha1-v6rnymiHPuNcpKUvdz1zjQJ7cLg=}

  '@humanfs/core@0.19.1':
    resolution: {integrity: sha1-F8Vcp9Qmcz/jxWGQa4Fzwza0Cnc=}
    engines: {node: '>=18.18.0'}

  '@humanfs/node@0.16.6':
    resolution: {integrity: sha1-7ioQ6qvRExmHvwSI/ZuCAXTNdl4=}
    engines: {node: '>=18.18.0'}

  '@humanwhocodes/module-importer@1.0.1':
    resolution: {integrity: sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=}
    engines: {node: '>=12.22'}

  '@humanwhocodes/retry@0.3.1':
    resolution: {integrity: sha1-xypcdqn7rzSI4jGxPcUsDae6tCo=}
    engines: {node: '>=18.18'}

  '@humanwhocodes/retry@0.4.3':
    resolution: {integrity: sha1-wrnS43TuYsWG062+qHGZsdenpro=}
    engines: {node: '>=18.18'}

  '@isaacs/fs-minipass@4.0.1':
    resolution: {integrity: sha1-LVmuOrSzj7QnC/oj0w+OLobH/jI=}
    engines: {node: '>=18.0.0'}

  '@jridgewell/gen-mapping@0.3.8':
    resolution: {integrity: sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=}
    engines: {node: '>=6.0.0'}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=}

  '@mapbox/jsonlint-lines-primitives@2.0.2':
    resolution: {integrity: sha1-zlblOfg1UrWNENZy6k1vya3HsjQ=}
    engines: {node: '>= 0.6'}

  '@mapbox/unitbezier@0.0.1':
    resolution: {integrity: sha1-0y3rZscXfp6d/Du9aXCD4uZX/wE=}

  '@maplibre/maplibre-gl-style-spec@23.3.0':
    resolution: {integrity: sha1-tpq0jLOr6tTkkhM5bI+DSSY4uXw=}
    hasBin: true

  '@mui/core-downloads-tracker@7.1.0':
    resolution: {integrity: sha1-B2e62adKrrADLac5DR0cMmU+Mdg=}

  '@mui/icons-material@7.1.0':
    resolution: {integrity: sha1-wghg1PLwkWBpej3TUyNKgCxFsek=}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      '@mui/material': ^7.1.0
      '@types/react': ^17.0.0 || ^18.0.0 || ^19.0.0
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@mui/lab@7.0.0-beta.12':
    resolution: {integrity: sha1-4PyXdgeGgUxR/0cU1cfBwD4mT70=}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      '@emotion/react': ^11.5.0
      '@emotion/styled': ^11.3.0
      '@mui/material': ^7.1.0
      '@mui/material-pigment-css': ^7.1.0
      '@types/react': ^17.0.0 || ^18.0.0 || ^19.0.0
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/react':
        optional: true
      '@emotion/styled':
        optional: true
      '@mui/material-pigment-css':
        optional: true
      '@types/react':
        optional: true

  '@mui/material@7.1.0':
    resolution: {integrity: sha1-8lhm+1B62iorOsQzsTorx52D9gg=}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      '@emotion/react': ^11.5.0
      '@emotion/styled': ^11.3.0
      '@mui/material-pigment-css': ^7.1.0
      '@types/react': ^17.0.0 || ^18.0.0 || ^19.0.0
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/react':
        optional: true
      '@emotion/styled':
        optional: true
      '@mui/material-pigment-css':
        optional: true
      '@types/react':
        optional: true

  '@mui/private-theming@7.1.0':
    resolution: {integrity: sha1-JXuCzb1OxjEy8/wU4SHlrLP/bkA=}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      '@types/react': ^17.0.0 || ^18.0.0 || ^19.0.0
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@mui/styled-engine@7.1.0':
    resolution: {integrity: sha1-O/HXhWuYk0WF99A1gr10Bz2ypLQ=}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      '@emotion/react': ^11.4.1
      '@emotion/styled': ^11.3.0
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/react':
        optional: true
      '@emotion/styled':
        optional: true

  '@mui/system@7.1.0':
    resolution: {integrity: sha1-XswCSPx6j1r3crUilxBfzVEvHwM=}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      '@emotion/react': ^11.5.0
      '@emotion/styled': ^11.3.0
      '@types/react': ^17.0.0 || ^18.0.0 || ^19.0.0
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/react':
        optional: true
      '@emotion/styled':
        optional: true
      '@types/react':
        optional: true

  '@mui/types@7.4.2':
    resolution: {integrity: sha1-/euYVaSWjDYLzJmLnbqT5fY1tA8=}
    peerDependencies:
      '@types/react': ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@mui/utils@7.1.0':
    resolution: {integrity: sha1-RkwMG8j1fAfZNKxnTz3Mgawk9os=}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      '@types/react': ^17.0.0 || ^18.0.0 || ^19.0.0
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@mui/x-date-pickers@8.4.0':
    resolution: {integrity: sha1-rh2kXHYCWNWchCqSMzoo386rAJo=}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      '@emotion/react': ^11.9.0
      '@emotion/styled': ^11.8.1
      '@mui/material': ^5.15.14 || ^6.0.0 || ^7.0.0
      '@mui/system': ^5.15.14 || ^6.0.0 || ^7.0.0
      date-fns: ^2.25.0 || ^3.2.0 || ^4.0.0
      date-fns-jalali: ^2.13.0-0 || ^3.2.0-0 || ^4.0.0-0
      dayjs: ^1.10.7
      luxon: ^3.0.2
      moment: ^2.29.4
      moment-hijri: ^2.1.2 || ^3.0.0
      moment-jalaali: ^0.7.4 || ^0.8.0 || ^0.9.0 || ^0.10.0
      react: ^17.0.0 || ^18.0.0 || ^19.0.0
      react-dom: ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/react':
        optional: true
      '@emotion/styled':
        optional: true
      date-fns:
        optional: true
      date-fns-jalali:
        optional: true
      dayjs:
        optional: true
      luxon:
        optional: true
      moment:
        optional: true
      moment-hijri:
        optional: true
      moment-jalaali:
        optional: true

  '@mui/x-internals@8.4.0':
    resolution: {integrity: sha1-WTs2yrcFD+Hkyo3DzkVTSaPtrl0=}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      react: ^17.0.0 || ^18.0.0 || ^19.0.0

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=}
    engines: {node: '>= 8'}

  '@opentelemetry/api-logs@0.201.1':
    resolution: {integrity: sha1-3rOdXH5r8Isejt49m+FGKcnMYxQ=}
    engines: {node: '>=8.0.0'}

  '@opentelemetry/api@1.9.0':
    resolution: {integrity: sha1-0D66aCc9wPdQnio9XLoh6uEDef4=}
    engines: {node: '>=8.0.0'}

  '@opentelemetry/core@2.0.1':
    resolution: {integrity: sha1-ROEUnVZmpHQ83pQ++JhB2zzg+Lw=}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': '>=1.0.0 <1.10.0'

  '@opentelemetry/exporter-trace-otlp-http@0.201.1':
    resolution: {integrity: sha1-6/Vnba/dmkMGboNi0W0B4CMg++k=}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-fetch@0.201.1':
    resolution: {integrity: sha1-IR0SWcSrJPOeWo8W8Z7frsuaU3M=}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation-xml-http-request@0.201.1':
    resolution: {integrity: sha1-q5hq/hTg5meY21ew0NiUpjZI/4M=}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/instrumentation@0.201.1':
    resolution: {integrity: sha1-ovcqmreRhm0sia1dYm3/lhN1On4=}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/otlp-exporter-base@0.201.1':
    resolution: {integrity: sha1-LXVZcCTkdFm1xef6JJNJaYIO8EA=}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/otlp-transformer@0.201.1':
    resolution: {integrity: sha1-fu9Xg0ozuD5MyrMg+INw6LCFojU=}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': ^1.3.0

  '@opentelemetry/resources@2.0.1':
    resolution: {integrity: sha1-A2XRNCkcDtGNlkRKHiHQ5qSByEA=}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': '>=1.3.0 <1.10.0'

  '@opentelemetry/sdk-logs@0.201.1':
    resolution: {integrity: sha1-2/TGjdzbeQdkfjHhnlEDNrBN8QI=}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': '>=1.4.0 <1.10.0'

  '@opentelemetry/sdk-metrics@2.0.1':
    resolution: {integrity: sha1-77bpNJ6KkDisYi4XJpK/zcrYAQs=}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': '>=1.9.0 <1.10.0'

  '@opentelemetry/sdk-trace-base@2.0.1':
    resolution: {integrity: sha1-JYCLtqPQilAa2EAknk1D00k+tuU=}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': '>=1.3.0 <1.10.0'

  '@opentelemetry/sdk-trace-web@2.0.1':
    resolution: {integrity: sha1-rW9ZDLwaGi6ACjgVvWsZI8jHik0=}
    engines: {node: ^18.19.0 || >=20.6.0}
    peerDependencies:
      '@opentelemetry/api': '>=1.0.0 <1.10.0'

  '@opentelemetry/semantic-conventions@1.33.1':
    resolution: {integrity: sha1-2gb6Q4cykoasOA31KpOxSU/wcn0=}
    engines: {node: '>=14'}

  '@petamoriken/float16@3.9.2':
    resolution: {integrity: sha1-IXpdNJ82Vbjihr5Efg7R6uBjp48=}

  '@pkgr/core@0.2.4':
    resolution: {integrity: sha1-2JcXCisLpR94oJntzNlo97EDOHw=}
    engines: {node: ^12.20.0 || ^14.18.0 || >=16.0.0}

  '@playwright/test@1.52.0':
    resolution: {integrity: sha1-Jn7FlbQ6j0+l5ETqUDaJYp6Rpbg=}
    engines: {node: '>=18'}
    hasBin: true

  '@popperjs/core@2.11.8':
    resolution: {integrity: sha1-a3kDLnYKCJnNQgRxC+7elyo6GF8=}

  '@protobufjs/aspromise@1.1.2':
    resolution: {integrity: sha1-m4sMxmPWaafY9vXQiToU00jzD78=}

  '@protobufjs/base64@1.1.2':
    resolution: {integrity: sha1-TIVzDlm5ofHzSQR9vyQpYDS7JzU=}

  '@protobufjs/codegen@2.0.4':
    resolution: {integrity: sha1-fvN/DQEPsCitGtWXIuUG2SYoFcs=}

  '@protobufjs/eventemitter@1.1.0':
    resolution: {integrity: sha1-NVy8mLr61ZePntCV85diHx0Ga3A=}

  '@protobufjs/fetch@1.1.0':
    resolution: {integrity: sha1-upn7WYYUr2VwDBYZ/wbUVLDYTEU=}

  '@protobufjs/float@1.0.2':
    resolution: {integrity: sha1-Xp4avctz/Ap8uLKR33jIy9l7h9E=}

  '@protobufjs/inquire@1.1.0':
    resolution: {integrity: sha1-/yAOPnzyQp4tyvwRQIKOjMY48Ik=}

  '@protobufjs/path@1.1.2':
    resolution: {integrity: sha1-bMKyDFya1q0NzP0hynZz2Nf79o0=}

  '@protobufjs/pool@1.1.0':
    resolution: {integrity: sha1-Cf0V8tbTq/qbZbw2ZQbWrXhG/1Q=}

  '@protobufjs/utf8@1.1.0':
    resolution: {integrity: sha1-p3c2C1s5oaLlEG+OhY8v0tBgxXA=}

  '@radix-ui/number@1.1.1':
    resolution: {integrity: sha1-eyySJfvxsSZTlVH1mFdp0ASNkJA=}

  '@radix-ui/primitive@1.1.2':
    resolution: {integrity: sha1-g/QVxEJfIePSeRTBKzJyoy49rmU=}

  '@radix-ui/react-accordion@1.2.11':
    resolution: {integrity: sha1-eDfdTUSu7VaqutKwmHJ7i0+Jrkw=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-arrow@1.1.7':
    resolution: {integrity: sha1-4UomV8gdlhWYxecrc91gmKzATwk=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-avatar@1.1.10':
    resolution: {integrity: sha1-xYqIAO89PueDsxaP7nx29lNL/ZM=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-checkbox@1.3.2':
    resolution: {integrity: sha1-KAlyRNloqo+TJJsNPfAqFy/UvuU=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-collapsible@1.1.11':
    resolution: {integrity: sha1-otEy1bqm8UVR8Vsf/yn5Jcrka4M=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-collection@1.1.7':
    resolution: {integrity: sha1-0FwlyprEaVzBm6kfQvaG4+otmuw=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-compose-refs@1.1.2':
    resolution: {integrity: sha1-osTEevYzcEjueP9twNCQs5DSuzA=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-context@1.1.2':
    resolution: {integrity: sha1-YWKO8mmkMzgsNk9vHjeIptwhOjY=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-dialog@1.1.14':
    resolution: {integrity: sha1-TGnIDCWLxlYTmM/OBVIC6hEHUQc=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-direction@1.1.1':
    resolution: {integrity: sha1-OeWldp5nbHUyBLeS++bPUI5VChQ=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-dismissable-layer@1.1.10':
    resolution: {integrity: sha1-QpubraNnLGiVpdamQqym7K9PGMM=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-dropdown-menu@2.1.15':
    resolution: {integrity: sha1-9QcyDejhG8HmcabsDCenqJ5yUTE=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-focus-guards@1.1.2':
    resolution: {integrity: sha1-Tsmn5Qkl9/tmE5RGAEW0YhKjO+0=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-focus-scope@1.1.7':
    resolution: {integrity: sha1-3+dvwQNTfYC/QnI6GDdz/Qe/tY0=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-id@1.1.1':
    resolution: {integrity: sha1-FAQALnmgP+Bit+OGSqAeJL0Ucfc=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-label@2.1.7':
    resolution: {integrity: sha1-rZWf+cbklo1TMynrlWluG6itcqs=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-menu@2.1.15':
    resolution: {integrity: sha1-oajwbKs8MJ+ZmM29KzrSeeQu1IM=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-popover@1.1.14':
    resolution: {integrity: sha1-VJbRmG8Ch838d+c/cKiH5Mt3rQg=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-popper@1.2.7':
    resolution: {integrity: sha1-Uxzy7rs9MnDVj32BNuRRdkZCmXg=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-portal@1.1.9':
    resolution: {integrity: sha1-FMNkn+SOxHSsUe2fK59dpNkcRHI=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-presence@1.1.4':
    resolution: {integrity: sha1-JTrArUlGxbSpxmh4M19c8HyWfO0=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-primitive@2.1.3':
    resolution: {integrity: sha1-25uLz/SeAb5RCteYk/sOTNpQ8bw=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-roving-focus@1.1.10':
    resolution: {integrity: sha1-RgMEltKkkMSXnSmn4SUkZeUeSws=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-select@2.2.5':
    resolution: {integrity: sha1-ni+luPTMmbhu9bujy5tzgor7UfA=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-separator@1.1.7':
    resolution: {integrity: sha1-oYvX/QfBD9obuhTyowMuexorNHA=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-slider@1.3.5':
    resolution: {integrity: sha1-+cB03A3ShQqkJgnnLedGQqSFG3k=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-slot@1.2.3':
    resolution: {integrity: sha1-UC1uNU/IR9QWnDvF8Yned39oz+E=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-tabs@1.1.12':
    resolution: {integrity: sha1-mbNSLHPbkmP0KabQ9amsuI3zsSk=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-tooltip@1.2.7':
    resolution: {integrity: sha1-I2Eqx6Xo4faCnkbQ4K2Ur+OXbHI=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/react-use-callback-ref@1.1.1':
    resolution: {integrity: sha1-YqTbqLMlX9xcx3h/rqwcbkzFjUA=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-controllable-state@1.2.2':
    resolution: {integrity: sha1-kFeTQF3lfWGkOfSv67sX0GRfMZA=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-effect-event@0.0.2':
    resolution: {integrity: sha1-CQzzDQCkx2MqFVSFEukVIhdZOQc=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-escape-keydown@1.1.1':
    resolution: {integrity: sha1-s/7Zu+o2ahGPQEJ6xAUAqhQjzCk=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-is-hydrated@0.1.0':
    resolution: {integrity: sha1-VE2nM2lRcDbHdlnXzdAZ3A9f+aA=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-layout-effect@1.1.1':
    resolution: {integrity: sha1-DEIwqe7UnUWJyWfi2cDZ1gojlx4=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-previous@1.1.1':
    resolution: {integrity: sha1-GhrVVolz0kBR7Qr2h3ZvbHy5tbU=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-rect@1.1.1':
    resolution: {integrity: sha1-AUQ8qO0HHTMCPBET5Rc7Xth2kVI=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-use-size@1.1.1':
    resolution: {integrity: sha1-beJ2/7w4mlN//kMW9bDyQSlAWzc=}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@radix-ui/react-visually-hidden@1.2.3':
    resolution: {integrity: sha1-qMOMhgdzXcnwXDL4erD5wrEJ778=}
    peerDependencies:
      '@types/react': '*'
      '@types/react-dom': '*'
      react: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
      react-dom: ^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@radix-ui/rect@1.1.1':
    resolution: {integrity: sha1-eCRO/hKTDFb9JV15I4ZYV8QayMs=}

  '@react-hook/debounce@3.0.0':
    resolution: {integrity: sha1-nuqLXYHUy2fNct2GV7P/ckr8fK0=}
    peerDependencies:
      react: '>=16.8'

  '@react-hook/event@1.2.6':
    resolution: {integrity: sha1-UvkVeK3ZNKzBIDMoygmrFPx+5Y4=}
    peerDependencies:
      react: '>=16.8'

  '@react-hook/latest@1.0.3':
    resolution: {integrity: sha1-wtHQsK+LaexuKzokEroHaKyC24A=}
    peerDependencies:
      react: '>=16.8'

  '@react-hook/passive-layout-effect@1.2.1':
    resolution: {integrity: sha1-wG2sLQEfNtYSWaocbfTw1eKLxV4=}
    peerDependencies:
      react: '>=16.8'

  '@react-hook/throttle@2.2.0':
    resolution: {integrity: sha1-0EAnFKBuG6C8HaH99cPFzQ4I1Fo=}
    peerDependencies:
      react: '>=16.8'

  '@react-hook/window-scroll@1.3.0':
    resolution: {integrity: sha1-B5FCOaRSB1x+so0V7Q4v1Fqi1ls=}
    peerDependencies:
      react: '>=16.8'

  '@react-hook/window-size@3.1.1':
    resolution: {integrity: sha1-GlZJAs/iEujCf/wudK3KoYp9ZbM=}
    peerDependencies:
      react: '>=16.8'

  '@remix-run/router@1.23.0':
    resolution: {integrity: sha1-NTkNDnd5YmwCaxE3baZ4nrg4kkI=}
    engines: {node: '>=14.0.0'}

  '@rollup/rollup-android-arm-eabi@4.41.0':
    resolution: {integrity: sha1-kUWzj68/v+PsVXEwEQ53L3lzNao=}
    cpu: [arm]
    os: [android]

  '@rollup/rollup-android-arm64@4.41.0':
    resolution: {integrity: sha1-1z1kHFnp14J+XOCvnfvBaLlczg8=}
    cpu: [arm64]
    os: [android]

  '@rollup/rollup-darwin-arm64@4.41.0':
    resolution: {integrity: sha1-RdnXHZQRF8mOel539g8LxoLSfoI=}
    cpu: [arm64]
    os: [darwin]

  '@rollup/rollup-darwin-x64@4.41.0':
    resolution: {integrity: sha1-jXL7X4FxTLQ+kPJj+xZ0UgzOPyo=}
    cpu: [x64]
    os: [darwin]

  '@rollup/rollup-freebsd-arm64@4.41.0':
    resolution: {integrity: sha1-pStYhSyc7JJV44Ki8zWwi8jGER0=}
    cpu: [arm64]
    os: [freebsd]

  '@rollup/rollup-freebsd-x64@4.41.0':
    resolution: {integrity: sha1-EEUR3GRhJ4nd2kHRZKsHzayEpsE=}
    cpu: [x64]
    os: [freebsd]

  '@rollup/rollup-linux-arm-gnueabihf@4.41.0':
    resolution: {integrity: sha1-ZD460ZyTkDIB/eiavXa6rucl5sI=}
    cpu: [arm]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm-musleabihf@4.41.0':
    resolution: {integrity: sha1-/calla7Hsgxb/ayBQSAoxW1zTmM=}
    cpu: [arm]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-arm64-gnu@4.41.0':
    resolution: {integrity: sha1-woYgvNOFSWvbvCSSCyH5/Mqey/o=}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-arm64-musl@4.41.0':
    resolution: {integrity: sha1-prcbHo+jO6yfZbb4eejth4A10SA=}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-loongarch64-gnu@4.41.0':
    resolution: {integrity: sha1-sGN0YBzoZaERAySy8G21dNOhsOE=}
    cpu: [loong64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-powerpc64le-gnu@4.41.0':
    resolution: {integrity: sha1-iiofYFjJIIicKv83U6IP6teozCY=}
    cpu: [ppc64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-riscv64-gnu@4.41.0':
    resolution: {integrity: sha1-jvb2gNARuVovZUbGwxo3ozE4A18=}
    cpu: [riscv64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-riscv64-musl@4.41.0':
    resolution: {integrity: sha1-n0iExZVafNObOW9uJ6pZsyaZiOs=}
    cpu: [riscv64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-linux-s390x-gnu@4.41.0':
    resolution: {integrity: sha1-VhkwPMUZlOPfQEpJf0LHncXv1us=}
    cpu: [s390x]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-gnu@4.41.0':
    resolution: {integrity: sha1-w+QrZsBOJa0PKgC+7ELt6WzMiYM=}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@rollup/rollup-linux-x64-musl@4.41.0':
    resolution: {integrity: sha1-jTRS3kKqcvxfw+WtHrC2gDB0KiU=}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@rollup/rollup-win32-arm64-msvc@4.41.0':
    resolution: {integrity: sha1-O3u9n0Pxw4AGHzBqvObz9k3iAwY=}
    cpu: [arm64]
    os: [win32]

  '@rollup/rollup-win32-ia32-msvc@4.41.0':
    resolution: {integrity: sha1-4n71xAu+xJ+sPU5LFhj75Fl7QOU=}
    cpu: [ia32]
    os: [win32]

  '@rollup/rollup-win32-x64-msvc@4.41.0':
    resolution: {integrity: sha1-sLWVrUcgJZu7gWAHUNJqZVysBr4=}
    cpu: [x64]
    os: [win32]

  '@snap/design-system@1.2.4':
    resolution: {integrity: sha1-Y57MZGldj4ca20sWlhDn6udjGhM=}
    engines: {pnpm: '>=10'}
    peerDependencies:
      react: ^19.0.0
      react-dom: ^19.0.0

  '@tailwindcss/node@4.1.7':
    resolution: {integrity: sha1-bS3wbGuEpv2CVaU1tPU3xSNaN+4=}

  '@tailwindcss/node@4.1.8':
    resolution: {integrity: sha1-4pGHq+xhlM4enwciCMYhFqeaEps=}

  '@tailwindcss/oxide-android-arm64@4.1.7':
    resolution: {integrity: sha1-OxFQl7ZK/2SHcVME6+C7Q9ei1Co=}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [android]

  '@tailwindcss/oxide-android-arm64@4.1.8':
    resolution: {integrity: sha1-TLS0ZGNvx+MVShu33zioKCkbPpo=}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [android]

  '@tailwindcss/oxide-darwin-arm64@4.1.7':
    resolution: {integrity: sha1-bJ2M081jGirA3/WKdmyVjP9bAEY=}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]

  '@tailwindcss/oxide-darwin-arm64@4.1.8':
    resolution: {integrity: sha1-sLjAJ0X3aupoPDCBjiSdYoIYZLg=}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]

  '@tailwindcss/oxide-darwin-x64@4.1.7':
    resolution: {integrity: sha1-oCK17wBlcKyLuSEo7H/JzCMUvdE=}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]

  '@tailwindcss/oxide-darwin-x64@4.1.8':
    resolution: {integrity: sha1-0PP6TDveIady4p4xyXOdkdt53hI=}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]

  '@tailwindcss/oxide-freebsd-x64@4.1.7':
    resolution: {integrity: sha1-78Ez5gZcPGKZqYHKqcX0JuHWDl4=}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [freebsd]

  '@tailwindcss/oxide-freebsd-x64@4.1.8':
    resolution: {integrity: sha1-VFyUyUEAftGqLkSUZVAbcNWcs9o=}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [freebsd]

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.7':
    resolution: {integrity: sha1-ek3B2WNsGx5ik2vWeaiGcDDcCtY=}
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [linux]

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.8':
    resolution: {integrity: sha1-4b2/Y6F5CBZpuM0clSOIl3R2Drk=}
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [linux]

  '@tailwindcss/oxide-linux-arm64-gnu@4.1.7':
    resolution: {integrity: sha1-mg2gCcN9E1/pg9EJO/ttNw/pJtw=}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@tailwindcss/oxide-linux-arm64-gnu@4.1.8':
    resolution: {integrity: sha1-jSgJO71Dva53Gi3MpyDpJrqlcJM=}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  '@tailwindcss/oxide-linux-arm64-musl@4.1.7':
    resolution: {integrity: sha1-7yOYtIxCYUjBuZSf292G02TcoQ0=}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@tailwindcss/oxide-linux-arm64-musl@4.1.8':
    resolution: {integrity: sha1-zGzs6BTYE4herZzYudVa6z21bJc=}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  '@tailwindcss/oxide-linux-x64-gnu@4.1.7':
    resolution: {integrity: sha1-TTauxMTfh/jDMlJrtodLLDYjCQE=}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@tailwindcss/oxide-linux-x64-gnu@4.1.8':
    resolution: {integrity: sha1-TKwU+nE4JXR3P7eYbZ8Gga2J494=}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  '@tailwindcss/oxide-linux-x64-musl@4.1.7':
    resolution: {integrity: sha1-bjBF7ccNUVYImroM1Kl2DKpTllw=}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@tailwindcss/oxide-linux-x64-musl@4.1.8':
    resolution: {integrity: sha1-4IXxzLyPl2JXc6ajr8Km+I7fWdo=}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  '@tailwindcss/oxide-wasm32-wasi@4.1.7':
    resolution: {integrity: sha1-ebQbDI2m4vHcW3Iv5DUoqjJCItU=}
    engines: {node: '>=14.0.0'}
    cpu: [wasm32]
    bundledDependencies:
      - '@napi-rs/wasm-runtime'
      - '@emnapi/core'
      - '@emnapi/runtime'
      - '@tybys/wasm-util'
      - '@emnapi/wasi-threads'
      - tslib

  '@tailwindcss/oxide-wasm32-wasi@4.1.8':
    resolution: {integrity: sha1-xeGf/+Z/Jcq/EqNXu6TocSgVHqA=}
    engines: {node: '>=14.0.0'}
    cpu: [wasm32]
    bundledDependencies:
      - '@napi-rs/wasm-runtime'
      - '@emnapi/core'
      - '@emnapi/runtime'
      - '@tybys/wasm-util'
      - '@emnapi/wasi-threads'
      - tslib

  '@tailwindcss/oxide-win32-arm64-msvc@4.1.7':
    resolution: {integrity: sha1-bGlbhw64o/mpWNCvzMZ7G27n540=}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]

  '@tailwindcss/oxide-win32-arm64-msvc@4.1.8':
    resolution: {integrity: sha1-d1IfI/kWBMWHc2kn/Sy1JmZ7c0Q=}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [win32]

  '@tailwindcss/oxide-win32-x64-msvc@4.1.7':
    resolution: {integrity: sha1-ASspKfbzO6cgpIeT49urw0uwISw=}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]

  '@tailwindcss/oxide-win32-x64-msvc@4.1.8':
    resolution: {integrity: sha1-Vch2qzX4d50dzuxhSDzZg01zZaw=}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]

  '@tailwindcss/oxide@4.1.7':
    resolution: {integrity: sha1-2DcMTVJKMBeoXU9jxByiMYdw3r0=}
    engines: {node: '>= 10'}

  '@tailwindcss/oxide@4.1.8':
    resolution: {integrity: sha1-t6PfEMbEesWjrJl2rTNHMsSHDRY=}
    engines: {node: '>= 10'}

  '@tailwindcss/postcss@4.1.8':
    resolution: {integrity: sha1-sSN0tJ86zNm9kCpDJNEk5ngDNQ8=}

  '@tailwindcss/vite@4.1.7':
    resolution: {integrity: sha1-c/tV0jQZgvuSD5FuHvZ4EJnPLfY=}
    peerDependencies:
      vite: ^5.2.0 || ^6

  '@tanstack/eslint-plugin-query@5.74.7':
    resolution: {integrity: sha1-5zQz7YuiOZ3FaiJa5Om3kwCHiqw=}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0

  '@tanstack/query-core@5.76.0':
    resolution: {integrity: sha1-O01dNM4we6DPfRo+kNetzcbEa+A=}

  '@tanstack/query-devtools@5.76.0':
    resolution: {integrity: sha1-ukN1TtjSOiZe1y8X3mGPqfnHZJ0=}

  '@tanstack/react-query-devtools@5.76.1':
    resolution: {integrity: sha1-IBV6WIDfX9TU/o/U/KLIZj2N+j4=}
    peerDependencies:
      '@tanstack/react-query': ^5.76.1
      react: ^18 || ^19

  '@tanstack/react-query@5.76.1':
    resolution: {integrity: sha1-rIoZ+Z3+wUUqRP4i1GaAw5bCEVI=}
    peerDependencies:
      react: ^18 || ^19

  '@testing-library/dom@10.4.0':
    resolution: {integrity: sha1-gqnZRi8R0kDsrb9AZgfGzu7/Q6g=}
    engines: {node: '>=18'}

  '@testing-library/jest-dom@6.6.3':
    resolution: {integrity: sha1-JrqQbPkowPgXLhgsb+IU60+fK9I=}
    engines: {node: '>=14', npm: '>=6', yarn: '>=1'}

  '@testing-library/react@16.3.0':
    resolution: {integrity: sha1-OoW7m96/GAzXbboWRU4kJWTVmKY=}
    engines: {node: '>=18'}
    peerDependencies:
      '@testing-library/dom': ^10.0.0
      '@types/react': ^18.0.0 || ^19.0.0
      '@types/react-dom': ^18.0.0 || ^19.0.0
      react: ^18.0.0 || ^19.0.0
      react-dom: ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true
      '@types/react-dom':
        optional: true

  '@testing-library/user-event@14.6.1':
    resolution: {integrity: sha1-E+CaMteotwYP44MEeI6/QZfNIUk=}
    engines: {node: '>=12', npm: '>=6'}
    peerDependencies:
      '@testing-library/dom': '>=7.21.4'

  '@transloadit/prettier-bytes@0.3.5':
    resolution: {integrity: sha1-DMqDl1KT4/SZAimRSULGlxQSLt4=}

  '@tsparticles/basic@3.8.1':
    resolution: {integrity: sha1-uow3LI6kYvc3p8qylsookulVESs=}

  '@tsparticles/engine@3.8.1':
    resolution: {integrity: sha1-HcU4zbBDdXyLipC9A7EGnBgBzB4=}

  '@tsparticles/interaction-external-attract@3.8.1':
    resolution: {integrity: sha1-eAgoK/AF+ZxxUOi4aqmHCo4eSd4=}

  '@tsparticles/interaction-external-bounce@3.8.1':
    resolution: {integrity: sha1-QdFMfmrDt6+tIlhRC1MLbflQd6I=}

  '@tsparticles/interaction-external-bubble@3.8.1':
    resolution: {integrity: sha1-GjI21kf0z9u90GNGbdlS6zi8Ql8=}

  '@tsparticles/interaction-external-connect@3.8.1':
    resolution: {integrity: sha1-yko+BgPLKym0pWSv3gZJV+lI+xo=}

  '@tsparticles/interaction-external-grab@3.8.1':
    resolution: {integrity: sha1-PxfjqIVzoPiwVMDaAhSJGPBFslg=}

  '@tsparticles/interaction-external-pause@3.8.1':
    resolution: {integrity: sha1-BCJ96q8rB4cka4Pg5XSrX7sPKLM=}

  '@tsparticles/interaction-external-push@3.8.1':
    resolution: {integrity: sha1-rWddw0s6rKQDjb5o1fuvvOLpNkg=}

  '@tsparticles/interaction-external-remove@3.8.1':
    resolution: {integrity: sha1-Un79BvkxI8OEaZ6ggAU9Zvozfr0=}

  '@tsparticles/interaction-external-repulse@3.8.1':
    resolution: {integrity: sha1-iUzi2EUZBlFtUOvqXAc4et0VQHU=}

  '@tsparticles/interaction-external-slow@3.8.1':
    resolution: {integrity: sha1-FCQ2SsD8Uj7Y7uzDmxkvsxFKEdc=}

  '@tsparticles/interaction-external-trail@3.8.1':
    resolution: {integrity: sha1-/TDq9YF+1hxPqlvH69A4bfNMHKg=}

  '@tsparticles/interaction-particles-attract@3.8.1':
    resolution: {integrity: sha1-o+Ilp/gWGRiMIvLAObbyM05sHjc=}

  '@tsparticles/interaction-particles-collisions@3.8.1':
    resolution: {integrity: sha1-GOV/M+9d5evJ/XYtJaI2bY/or9c=}

  '@tsparticles/interaction-particles-links@3.8.1':
    resolution: {integrity: sha1-Hwsq7mV8yW1Gwp84+6ArYC+1XZc=}

  '@tsparticles/move-base@3.8.1':
    resolution: {integrity: sha1-SpaBB27KCdX9Rc5NeG9E5d4cspE=}

  '@tsparticles/move-parallax@3.8.1':
    resolution: {integrity: sha1-6oDNOFP18m7NJrSUWLGSMlsWNfA=}

  '@tsparticles/plugin-absorbers@3.8.1':
    resolution: {integrity: sha1-8aAdPjmppBT3kac89xwcYY9onvw=}

  '@tsparticles/plugin-easing-quad@3.8.1':
    resolution: {integrity: sha1-hXiq4HKVuWJGSNdVditLUPUKxI0=}

  '@tsparticles/plugin-emitters-shape-circle@3.8.1':
    resolution: {integrity: sha1-sgYKw8tHKQtPDjSGPcQUoXT7HQg=}

  '@tsparticles/plugin-emitters-shape-square@3.8.1':
    resolution: {integrity: sha1-VdDGUccCNuFlazDOsdJqLDryls8=}

  '@tsparticles/plugin-emitters@3.8.1':
    resolution: {integrity: sha1-uEEVNjpdHqwi8GmYWfp9XRGTBX0=}

  '@tsparticles/plugin-hex-color@3.8.1':
    resolution: {integrity: sha1-YiwJOEWq/mHZMlprd0mPSUMncyE=}

  '@tsparticles/plugin-hsl-color@3.8.1':
    resolution: {integrity: sha1-YaWwU+598yoRqHv3eUlZFGf4hiU=}

  '@tsparticles/plugin-rgb-color@3.8.1':
    resolution: {integrity: sha1-8c153llf7W8XESsUFFBXhT9ZI3o=}

  '@tsparticles/react@3.0.0':
    resolution: {integrity: sha1-0KeUulMWTrOc6Io5Yb5yOmNrN8A=}
    peerDependencies:
      '@tsparticles/engine': ^3.0.2
      react: '>=16.8.0'
      react-dom: '>=16.8.0'

  '@tsparticles/shape-circle@3.8.1':
    resolution: {integrity: sha1-AeWL1KLW/SygbKDqEvkCHp8naHg=}

  '@tsparticles/shape-emoji@3.8.1':
    resolution: {integrity: sha1-izM0oxhmF+KV3mF8tzDeQ143YvI=}

  '@tsparticles/shape-image@3.8.1':
    resolution: {integrity: sha1-lg8OvJS4dVt7jqFUWrGUwNjOO80=}

  '@tsparticles/shape-line@3.8.1':
    resolution: {integrity: sha1-wA+xjOsfO4ZJL5NLFfsLYbksK7I=}

  '@tsparticles/shape-polygon@3.8.1':
    resolution: {integrity: sha1-s7KLUjvQr2dmycpNPq7c6frK3Uc=}

  '@tsparticles/shape-square@3.8.1':
    resolution: {integrity: sha1-A1bpMduz7bV2ipLGvhK4mJYtpfs=}

  '@tsparticles/shape-star@3.8.1':
    resolution: {integrity: sha1-IZH01LJS5xJGnrD4WlowdRfyYB4=}

  '@tsparticles/shape-text@3.8.1':
    resolution: {integrity: sha1-XBhlaAkK2Aelx8WYYshp1f+rbIA=}

  '@tsparticles/slim@3.8.1':
    resolution: {integrity: sha1-GYzIC7/Xh6PlXaHzXynWqvbeSyQ=}

  '@tsparticles/updater-color@3.8.1':
    resolution: {integrity: sha1-81lUFZqGcb5qQQJkTt5nM3DxZOE=}

  '@tsparticles/updater-destroy@3.8.1':
    resolution: {integrity: sha1-aPty+7973TjzdBYDSYYXBXWbxsU=}

  '@tsparticles/updater-life@3.8.1':
    resolution: {integrity: sha1-VPldwdFnQKb/LidVhWV6dGjX2fs=}

  '@tsparticles/updater-opacity@3.8.1':
    resolution: {integrity: sha1-/+u/kmCu3jGUaCvC83cYXL+QLP0=}

  '@tsparticles/updater-out-modes@3.8.1':
    resolution: {integrity: sha1-jecOKFEM1Qodd9enkQsg06HGHus=}

  '@tsparticles/updater-roll@3.8.1':
    resolution: {integrity: sha1-B3bTPU1WFp+YeB2G2PERwttAzqc=}

  '@tsparticles/updater-rotate@3.8.1':
    resolution: {integrity: sha1-EqzO4BKKsb+bsCJYlkPqkO0B/8c=}

  '@tsparticles/updater-size@3.8.1':
    resolution: {integrity: sha1-sbf1GX+mmxNGvtmi2mh3HzXQqIw=}

  '@tsparticles/updater-stroke-color@3.8.1':
    resolution: {integrity: sha1-q2vfUDWnFNSdrW772UpXHt+mf+8=}

  '@tsparticles/updater-tilt@3.8.1':
    resolution: {integrity: sha1-SNLue2x8ljk2+XlSMybO5KO1b2w=}

  '@tsparticles/updater-twinkle@3.8.1':
    resolution: {integrity: sha1-cnv62JoI8bbXEnjb46JCZOAeos0=}

  '@tsparticles/updater-wobble@3.8.1':
    resolution: {integrity: sha1-iIDgi1guYWF36ADQp7JOPR5E7Fo=}

  '@types/aria-query@5.0.4':
    resolution: {integrity: sha1-GjHD03iFDSd42rtjdNA23LpLpwg=}

  '@types/babel__core@7.20.5':
    resolution: {integrity: sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=}

  '@types/babel__generator@7.27.0':
    resolution: {integrity: sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=}

  '@types/babel__template@7.4.4':
    resolution: {integrity: sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=}

  '@types/babel__traverse@7.20.7':
    resolution: {integrity: sha1-lozcI2bsPaFZ9hFmQo7kDzcOVsI=}

  '@types/chroma-js@3.1.1':
    resolution: {integrity: sha1-ksrFf7MtZCzhVtvEwFK146OiXbE=}

  '@types/conventional-commits-parser@5.0.1':
    resolution: {integrity: sha1-jLgc8XCFNJbLxQGjsy3PXkb/tho=}

  '@types/estree@1.0.7':
    resolution: {integrity: sha1-QVjTEFJ2dz1bdpXNSDSxci5PN6g=}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=}

  '@types/node@22.15.21':
    resolution: {integrity: sha1-GW7xT+INh/fK8eezmDJ2f5qZW3c=}

  '@types/parse-json@4.0.2':
    resolution: {integrity: sha1-WVDlCWB5MFWEXpVsQn/CsNcMUjk=}

  '@types/prop-types@15.7.14':
    resolution: {integrity: sha1-FDNBnXOyp+v8aRjc79LsDVzWmPI=}

  '@types/rbush@4.0.0':
    resolution: {integrity: sha1-sye/VJUunJJOpnAsNpBMLOHUfzU=}

  '@types/react-dom@19.1.5':
    resolution: {integrity: sha1-zf4sZjdCiHNy9UgEsW6NvCa9eUo=}
    peerDependencies:
      '@types/react': ^19.0.0

  '@types/react-transition-group@4.4.12':
    resolution: {integrity: sha1-tddlaEhbAqMHI4Jwv+lstR7ioEQ=}
    peerDependencies:
      '@types/react': '*'

  '@types/react@19.1.5':
    resolution: {integrity: sha1-n+s73rUG0MedhTO269ys28tHVts=}

  '@types/retry@0.12.2':
    resolution: {integrity: sha1-7SeaZPpDi7afJIDtpEk3kSu3SAo=}

  '@types/shimmer@1.2.0':
    resolution: {integrity: sha1-m3Bq+W+gZBaCiEI5enDfu/HBTe0=}

  '@typescript-eslint/eslint-plugin@8.32.1':
    resolution: {integrity: sha1-kYWz6qOwg9gxiRDhLVbGizxPRbQ=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      '@typescript-eslint/parser': ^8.0.0 || ^8.0.0-alpha.0
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/parser@8.32.1':
    resolution: {integrity: sha1-GLDlMxXgvCKyYZ05iuSaloNwk14=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/scope-manager@8.32.1':
    resolution: {integrity: sha1-mmv1+yxTgOFP6dOMysbku+F+ivw=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/type-utils@8.32.1':
    resolution: {integrity: sha1-uSkqRfaezbfbdNFpblfRqJUU0h4=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/types@8.32.1':
    resolution: {integrity: sha1-sZ/krA3Agxe64M6ewRaBI1dsHUs=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@typescript-eslint/typescript-estree@8.32.1':
    resolution: {integrity: sha1-kCNyDKTs9PWcJ1oFtf7WmxJ2+s4=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/utils@8.32.1':
    resolution: {integrity: sha1-TW1dKbnlGemoXpp06fe9tYq+lwQ=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  '@typescript-eslint/visitor-keys@8.32.1':
    resolution: {integrity: sha1-QyE5XMVcLrRgNsu7A+EBmU0R3co=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  '@uppy/aws-s3@4.2.3':
    resolution: {integrity: sha1-M+1NvXgT7CsHaHIT9I8FQA2RFzg=}
    peerDependencies:
      '@uppy/core': ^4.4.1

  '@uppy/companion-client@4.4.2':
    resolution: {integrity: sha1-TjzZ+3H5ntYoNduDcAbv89OP6oc=}
    peerDependencies:
      '@uppy/core': ^4.4.5

  '@uppy/core@4.4.5':
    resolution: {integrity: sha1-CaFiw3iF81LwSXXuh70DN1K8OLs=}

  '@uppy/dashboard@4.3.4':
    resolution: {integrity: sha1-K2PFQkgzzzEaOW1LNdbMDfq0PEU=}
    peerDependencies:
      '@uppy/core': ^4.4.5

  '@uppy/drag-drop@4.1.3':
    resolution: {integrity: sha1-N7OVUHk7yqCYKhN5JSbqJxQYINc=}
    peerDependencies:
      '@uppy/core': ^4.4.5

  '@uppy/file-input@4.1.3':
    resolution: {integrity: sha1-pbJXgSbvlrV98SveCsELGsaoR88=}
    peerDependencies:
      '@uppy/core': ^4.4.5

  '@uppy/informer@4.2.1':
    resolution: {integrity: sha1-NcbYdQ8aOI/qUTB4IdVBBv3scZM=}
    peerDependencies:
      '@uppy/core': ^4.4.1

  '@uppy/locales@4.5.2':
    resolution: {integrity: sha1-7SViMvMsAz04c6WpStYiD5hHtYo=}

  '@uppy/progress-bar@4.2.1':
    resolution: {integrity: sha1-JY8GZdwMjI/9qKn8WL7VZf5A90E=}
    peerDependencies:
      '@uppy/core': ^4.4.1

  '@uppy/provider-views@4.4.3':
    resolution: {integrity: sha1-auJVpAPenGjFhyXUjh+4FdF4gCc=}
    peerDependencies:
      '@uppy/core': ^4.4.4

  '@uppy/react@4.2.3':
    resolution: {integrity: sha1-iMMT9+PKNbGNF6leGGlWAHJ8Ql4=}
    peerDependencies:
      '@uppy/core': ^4.4.4
      '@uppy/dashboard': ^4.3.3
      '@uppy/drag-drop': ^4.1.2
      '@uppy/file-input': ^4.1.2
      '@uppy/progress-bar': ^4.2.1
      '@uppy/status-bar': ^4.1.3
      react: ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@uppy/dashboard':
        optional: true
      '@uppy/drag-drop':
        optional: true
      '@uppy/file-input':
        optional: true
      '@uppy/progress-bar':
        optional: true
      '@uppy/status-bar':
        optional: true

  '@uppy/status-bar@4.1.3':
    resolution: {integrity: sha1-dwz9PRQIUEdifM0+llVHfNLmo6E=}
    peerDependencies:
      '@uppy/core': ^4.4.4

  '@uppy/store-default@4.2.0':
    resolution: {integrity: sha1-+5Go52Y5uvBjm/0xxgSDi78lKfM=}

  '@uppy/thumbnail-generator@4.1.1':
    resolution: {integrity: sha1-prkCoiQh0B8UMTvXL6ceY0VIuQY=}
    peerDependencies:
      '@uppy/core': ^4.4.1

  '@uppy/utils@6.1.4':
    resolution: {integrity: sha1-L++gLnawY/dBKng0vxvcgrMFwPQ=}

  '@vitejs/plugin-react@4.4.1':
    resolution: {integrity: sha1-19HpyWFtdTawlTY37f7nxsvi/g8=}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      vite: ^4.2.0 || ^5.0.0 || ^6.0.0

  '@vitest/expect@3.1.4':
    resolution: {integrity: sha1-g3ZRpxaC42EcPfe1ixV7pIW9gCk=}

  '@vitest/mocker@3.1.4':
    resolution: {integrity: sha1-c0QQIrhscpm/vRGp+y6Zp93Cuw4=}
    peerDependencies:
      msw: ^2.4.9
      vite: ^5.0.0 || ^6.0.0
    peerDependenciesMeta:
      msw:
        optional: true
      vite:
        optional: true

  '@vitest/pretty-format@3.1.4':
    resolution: {integrity: sha1-2j6YwlDN4845/o5wkzmBRgexheg=}

  '@vitest/runner@3.1.4':
    resolution: {integrity: sha1-GfoW6zl/UyW5m6ykjCvKbK3QmPo=}

  '@vitest/snapshot@3.1.4':
    resolution: {integrity: sha1-eJfUlgo89hf7DxfhgswVx+Pk7T8=}

  '@vitest/spy@3.1.4':
    resolution: {integrity: sha1-lLtWbafvbet8Th/Xm3jxmqVGW58=}

  '@vitest/utils@3.1.4':
    resolution: {integrity: sha1-+fINkvE4Sp1mVIxICIU5B2AEe14=}

  JSONStream@1.3.5:
    resolution: {integrity: sha1-MgjB8I06TZkmGrZPkjArwV4RHKA=}
    hasBin: true

  acorn-import-attributes@1.9.5:
    resolution: {integrity: sha1-frFVexugXvGLXtDsZ1kb+rBGiO8=}
    peerDependencies:
      acorn: ^8

  acorn-jsx@5.3.2:
    resolution: {integrity: sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn@8.14.1:
    resolution: {integrity: sha1-ch1dwQ99W1YJqJF3PUdzF5aTXfs=}
    engines: {node: '>=0.4.0'}
    hasBin: true

  agent-base@7.1.3:
    resolution: {integrity: sha1-KUNeuCG8QZRjOluJ5bxHA7r8JaE=}
    engines: {node: '>= 14'}

  ajv@6.12.6:
    resolution: {integrity: sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=}

  ajv@8.17.1:
    resolution: {integrity: sha1-N9mlx3ava8ktf0+VEOukwKYNEaY=}

  ansi-escapes@7.0.0:
    resolution: {integrity: sha1-APwZ9JG7sY4dSBuXhoIE+SEJv+c=}
    engines: {node: '>=18'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=}
    engines: {node: '>=8'}

  ansi-regex@6.1.0:
    resolution: {integrity: sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=}
    engines: {node: '>=12'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha1-7dgDYornHATIWuegkG7a00tkiTc=}
    engines: {node: '>=8'}

  ansi-styles@5.2.0:
    resolution: {integrity: sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=}
    engines: {node: '>=10'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=}
    engines: {node: '>=12'}

  argparse@2.0.1:
    resolution: {integrity: sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=}

  aria-hidden@1.2.6:
    resolution: {integrity: sha1-cwUcmwiBFMeVsepBTpwP/4dP/Bo=}
    engines: {node: '>=10'}

  aria-query@5.3.0:
    resolution: {integrity: sha1-ZQxWnkGtkLUbPX315e7Rx1ScED4=}

  aria-query@5.3.2:
    resolution: {integrity: sha1-k/gaQ0gOM6M48ZFjo9EKUMAdzVk=}
    engines: {node: '>= 0.4'}

  array-buffer-byte-length@1.0.2:
    resolution: {integrity: sha1-OE0So3KVrsN2mrAirTI6GKUcz4s=}
    engines: {node: '>= 0.4'}

  array-ify@1.0.0:
    resolution: {integrity: sha1-nlKHYrSpBmrRY6aWKjZEGOlibs4=}

  array-includes@3.1.8:
    resolution: {integrity: sha1-XjcMvhcv3V3WUwwdSq3aJSgbqX0=}
    engines: {node: '>= 0.4'}

  array.prototype.findlast@1.2.5:
    resolution: {integrity: sha1-Pk+8swoVp/W/ZM8vquItE5wuSQQ=}
    engines: {node: '>= 0.4'}

  array.prototype.flat@1.3.3:
    resolution: {integrity: sha1-U0qvnm6N15+2uamRf4Oe8exjr+U=}
    engines: {node: '>= 0.4'}

  array.prototype.flatmap@1.3.3:
    resolution: {integrity: sha1-cSzHkq5wNwrkBYYmRinjOqtd04s=}
    engines: {node: '>= 0.4'}

  array.prototype.tosorted@1.1.4:
    resolution: {integrity: sha1-/pVGeP9TA05xfqM1KgPwsLhvf/w=}
    engines: {node: '>= 0.4'}

  arraybuffer.prototype.slice@1.0.4:
    resolution: {integrity: sha1-nXYNhNvdBtDL+SyISWFaGnqzGDw=}
    engines: {node: '>= 0.4'}

  assertion-error@2.0.1:
    resolution: {integrity: sha1-9kGhlrM1aQsQcL8AtudZP+wZC/c=}
    engines: {node: '>=12'}

  async-function@1.0.0:
    resolution: {integrity: sha1-UJyfymDq+FA0xoKYOBiOTkyP+ys=}
    engines: {node: '>= 0.4'}

  asynckit@0.4.0:
    resolution: {integrity: sha1-x57Zf380y48robyXkLzDZkdLS3k=}

  attr-accept@2.2.5:
    resolution: {integrity: sha1-1wYdlY5tT5e/hmXGi3WFGgcTq14=}
    engines: {node: '>=4'}

  autoprefixer@10.4.21:
    resolution: {integrity: sha1-dxiUaOeorR2aN/vAjvyfSAzwqV0=}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0

  autosuggest-highlight@3.3.4:
    resolution: {integrity: sha1-1xtXW6jqtAta26c9+SROm6iMw4c=}

  available-typed-arrays@1.0.7:
    resolution: {integrity: sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=}
    engines: {node: '>= 0.4'}

  axios@1.9.0:
    resolution: {integrity: sha1-JVNOO3K1RUAHfTMEb3fjuNcIGQE=}

  babel-plugin-macros@3.1.0:
    resolution: {integrity: sha1-nvbcdN65NLTbNE3Jc+6FHRSMUME=}
    engines: {node: '>=10', npm: '>=6'}

  balanced-match@1.0.2:
    resolution: {integrity: sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=}

  brace-expansion@1.1.11:
    resolution: {integrity: sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=}

  brace-expansion@2.0.1:
    resolution: {integrity: sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=}

  braces@3.0.3:
    resolution: {integrity: sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=}
    engines: {node: '>=8'}

  browserslist@4.24.5:
    resolution: {integrity: sha1-qg9bhWD+gf3oTG3LOPdZuvug4Rs=}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  cac@6.7.14:
    resolution: {integrity: sha1-gE4eb1Bu42PLDjzLsJytXdmHCVk=}
    engines: {node: '>=8'}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha1-S1QowiK+mF15w9gmV0edvgtZstY=}
    engines: {node: '>= 0.4'}

  call-bind@1.0.8:
    resolution: {integrity: sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=}
    engines: {node: '>= 0.4'}

  call-bound@1.0.4:
    resolution: {integrity: sha1-I43pNdKippKSjFOMfM+pEGf9Bio=}
    engines: {node: '>= 0.4'}

  callsites@3.1.0:
    resolution: {integrity: sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=}
    engines: {node: '>=6'}

  caniuse-lite@1.0.30001718:
    resolution: {integrity: sha1-2uE6nIDVF8MMYZdRWpYTHBlNj4I=}

  chai@5.2.0:
    resolution: {integrity: sha1-E1juEGdjYkEUrd+EqwJpfkEcnAU=}
    engines: {node: '>=12'}

  chalk@3.0.0:
    resolution: {integrity: sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=}
    engines: {node: '>=8'}

  chalk@4.1.2:
    resolution: {integrity: sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=}
    engines: {node: '>=10'}

  chalk@5.4.1:
    resolution: {integrity: sha1-G0i/CWPsFY3OKqz2nAk64t0gktg=}
    engines: {node: ^12.17.0 || ^14.13 || >=16.0.0}

  check-error@2.1.1:
    resolution: {integrity: sha1-h+uHauce44j6BHH+Qj9JS+HZbMw=}
    engines: {node: '>= 16'}

  chownr@3.0.0:
    resolution: {integrity: sha1-mFXmTs0kCpzEJnzopKpdJKHaFeQ=}
    engines: {node: '>=18'}

  cjs-module-lexer@1.4.3:
    resolution: {integrity: sha1-D3lzHrjP4exyrNQGbvrJ1hmRsA0=}

  class-variance-authority@0.7.1:
    resolution: {integrity: sha1-QAinmKDkVTp4GlesUXfJ+10EN4c=}

  classnames@2.5.1:
    resolution: {integrity: sha1-undMYUvg8BbaEFyFjnFZ6ujnaHs=}

  cli-cursor@5.0.0:
    resolution: {integrity: sha1-JKSDHs9aawHd6zL7caSyCIsNzjg=}
    engines: {node: '>=18'}

  cli-truncate@4.0.0:
    resolution: {integrity: sha1-bMKKKST+6eJc6R6XPbVscGbmFyo=}
    engines: {node: '>=18'}

  cliui@8.0.1:
    resolution: {integrity: sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=}
    engines: {node: '>=12'}

  clsx@2.1.1:
    resolution: {integrity: sha1-7tOXyf2L2IK/sY3qtxAgSaLzKZk=}
    engines: {node: '>=6'}

  color-convert@2.0.1:
    resolution: {integrity: sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=}
    engines: {node: '>=7.0.0'}

  color-name@1.1.4:
    resolution: {integrity: sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=}

  colorette@2.0.20:
    resolution: {integrity: sha1-nreT5oMwZ/cjWQL807CZF6AAqVo=}

  combined-stream@1.0.8:
    resolution: {integrity: sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=}
    engines: {node: '>= 0.8'}

  commander@13.1.0:
    resolution: {integrity: sha1-d2Fn22jHjzjczh+bjXuLmkiKv0Y=}
    engines: {node: '>=18'}

  compare-func@2.0.0:
    resolution: {integrity: sha1-+2XnXtvd/S5WhVTotbBf/3pR/LM=}

  concat-map@0.0.1:
    resolution: {integrity: sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=}

  conventional-changelog-angular@7.0.0:
    resolution: {integrity: sha1-XuyO2/8VqpsWgKjc+9U+LX6yuno=}
    engines: {node: '>=16'}

  conventional-changelog-conventionalcommits@7.0.2:
    resolution: {integrity: sha1-ql2g8bJUMJSInoz3YW6+Go9ccNU=}
    engines: {node: '>=16'}

  conventional-commits-parser@5.0.0:
    resolution: {integrity: sha1-V/NZS4GtVNQMG0KA8EVU3yhifZo=}
    engines: {node: '>=16'}
    hasBin: true

  convert-source-map@1.9.0:
    resolution: {integrity: sha1-f6rmI1P7QhM2bQypg1jSLoNosF8=}

  convert-source-map@2.0.0:
    resolution: {integrity: sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=}

  cosmiconfig-typescript-loader@6.1.0:
    resolution: {integrity: sha1-f2RFA+HCv/kK7S0ppjcAjyeWRrs=}
    engines: {node: '>=v18'}
    peerDependencies:
      '@types/node': '*'
      cosmiconfig: '>=9'
      typescript: '>=5'

  cosmiconfig@7.1.0:
    resolution: {integrity: sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=}
    engines: {node: '>=10'}

  cosmiconfig@9.0.0:
    resolution: {integrity: sha1-NMP8WCh7kV866QWrbcPeJYtVrZ0=}
    engines: {node: '>=14'}
    peerDependencies:
      typescript: '>=4.9.5'
    peerDependenciesMeta:
      typescript:
        optional: true

  cross-spawn@7.0.6:
    resolution: {integrity: sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=}
    engines: {node: '>= 8'}

  css.escape@1.5.1:
    resolution: {integrity: sha1-QuJ9T6BK4y+TGktNQZH6nN3ul8s=}

  cssstyle@4.3.1:
    resolution: {integrity: sha1-aKPJ9acKqX1abr7MmAXlEfwCLrg=}
    engines: {node: '>=18'}

  csstype@3.1.3:
    resolution: {integrity: sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=}

  dargs@8.1.0:
    resolution: {integrity: sha1-o0hZ6lCcvORUheWqNW/vcL/McnI=}
    engines: {node: '>=12'}

  data-urls@5.0.0:
    resolution: {integrity: sha1-L3aQa84YJEKf/stpIPRaCzDwDd4=}
    engines: {node: '>=18'}

  data-view-buffer@1.0.2:
    resolution: {integrity: sha1-IRoDupXsr3eYqMcZjXlTYhH4hXA=}
    engines: {node: '>= 0.4'}

  data-view-byte-length@1.0.2:
    resolution: {integrity: sha1-noD3ylJFPOPpPSWjUxh2fqdwRzU=}
    engines: {node: '>= 0.4'}

  data-view-byte-offset@1.0.1:
    resolution: {integrity: sha1-BoMH+bcat2274QKROJ4CCFZgYZE=}
    engines: {node: '>= 0.4'}

  date-fns-tz@3.2.0:
    resolution: {integrity: sha1-ZH3FbTisM6Pje2Xp1cTNpa9eWOY=}
    peerDependencies:
      date-fns: ^3.0.0 || ^4.0.0

  date-fns@4.1.0:
    resolution: {integrity: sha1-ZLPYP/9aqAQ49bGmM8LoO4ocLRQ=}

  debug@4.4.1:
    resolution: {integrity: sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decimal.js@10.5.0:
    resolution: {integrity: sha1-DzccfPbEiYzgr7CYNttzzYIBDyI=}

  deep-eql@5.0.2:
    resolution: {integrity: sha1-S3VtjXcKklcwCCXVKiws/5nDo0E=}
    engines: {node: '>=6'}

  deep-is@0.1.4:
    resolution: {integrity: sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=}

  define-data-property@1.1.4:
    resolution: {integrity: sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=}
    engines: {node: '>= 0.4'}

  define-properties@1.2.1:
    resolution: {integrity: sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=}
    engines: {node: '>= 0.4'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha1-3zrhmayt+31ECqrgsp4icrJOxhk=}
    engines: {node: '>=0.4.0'}

  dequal@2.0.3:
    resolution: {integrity: sha1-JkQhTxmX057Q7g7OcjNUkKesZ74=}
    engines: {node: '>=6'}

  detect-libc@2.0.4:
    resolution: {integrity: sha1-8EcVuLqBXlO02BCWVbZQimhlp+g=}
    engines: {node: '>=8'}

  detect-node-es@1.1.0:
    resolution: {integrity: sha1-FjrN9kMzDKoLTNfCHn7ndV1vpJM=}

  doctrine@2.1.0:
    resolution: {integrity: sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=}
    engines: {node: '>=0.10.0'}

  dom-accessibility-api@0.5.16:
    resolution: {integrity: sha1-WnQp5gZus2ZNkR4z+w5F3o6whFM=}

  dom-accessibility-api@0.6.3:
    resolution: {integrity: sha1-mT6SXMHXPyxmLn113VpURSWaj9g=}

  dom-helpers@5.2.1:
    resolution: {integrity: sha1-2UAFNrK/giWtmP4FLgKUUaxA6QI=}

  dot-prop@5.3.0:
    resolution: {integrity: sha1-kMzOcIzZzYLMTcjD3dmr3VWyDog=}
    engines: {node: '>=8'}

  dunder-proto@1.0.1:
    resolution: {integrity: sha1-165mfh3INIL4tw/Q9u78UNow9Yo=}
    engines: {node: '>= 0.4'}

  earcut@3.0.1:
    resolution: {integrity: sha1-9gs/ZxxWV8yp0+ExxVJ8Xd4A7zg=}

  electron-to-chromium@1.5.155:
    resolution: {integrity: sha1-gJ3Qrprh24fDWODAwXwJov/EMtE=}

  emoji-regex@10.4.0:
    resolution: {integrity: sha1-A1U6/qgLOXV0nPyzb3dsomjkE9Q=}

  emoji-regex@8.0.0:
    resolution: {integrity: sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=}

  enhanced-resolve@5.18.1:
    resolution: {integrity: sha1-coqwgvi3toNt5R8WN6q107lWj68=}
    engines: {node: '>=10.13.0'}

  entities@6.0.0:
    resolution: {integrity: sha1-CcninLebCmRZqbnbnvtBisW7jlE=}
    engines: {node: '>=0.12'}

  env-paths@2.2.1:
    resolution: {integrity: sha1-QgOZ1BbOH76bwKB8Yvpo1n/Q+PI=}
    engines: {node: '>=6'}

  environment@1.1.0:
    resolution: {integrity: sha1-jobGaxgPNjx6sxF4fgJZZl9FqfE=}
    engines: {node: '>=18'}

  error-ex@1.3.2:
    resolution: {integrity: sha1-tKxAZIEH/c3PriQvQovqihTU8b8=}

  es-abstract@1.23.9:
    resolution: {integrity: sha1-W0WZS33nja2lwb6/E3lkazK51gY=}
    engines: {node: '>= 0.4'}

  es-define-property@1.0.1:
    resolution: {integrity: sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=}
    engines: {node: '>= 0.4'}

  es-iterator-helpers@1.2.1:
    resolution: {integrity: sha1-0d0PWBKQVMCtki5qmh5l7vQ1/nU=}
    engines: {node: '>= 0.4'}

  es-module-lexer@1.7.0:
    resolution: {integrity: sha1-kVlgFWGICoXyc0VgqQmbLDHlNyo=}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha1-HE8sSDcydZfOadLKGQp/3RcjOME=}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.1.0:
    resolution: {integrity: sha1-8x274MGDsAptJutjJcgQwP0YvU0=}
    engines: {node: '>= 0.4'}

  es-shim-unscopables@1.1.0:
    resolution: {integrity: sha1-Q43zVSDaxdEF85Q9knVJ6jsA9LU=}
    engines: {node: '>= 0.4'}

  es-to-primitive@1.3.0:
    resolution: {integrity: sha1-lsicgsxJ/YeUokg1uj4f+H8hThg=}
    engines: {node: '>= 0.4'}

  esbuild@0.25.4:
    resolution: {integrity: sha1-u5oWM01O8sM8cwGpJLi4YzUaCFQ=}
    engines: {node: '>=18'}
    hasBin: true

  escalade@3.2.0:
    resolution: {integrity: sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=}
    engines: {node: '>=6'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=}
    engines: {node: '>=10'}

  eslint-config-prettier@10.1.5:
    resolution: {integrity: sha1-AMGNciUEO2+85qZlaXN3mY1FN4I=}
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'

  eslint-plugin-import-helpers@2.0.1:
    resolution: {integrity: sha1-OlxFGw/jA5h5MbNpy4nWo7fsotU=}
    peerDependencies:
      eslint: 9.x

  eslint-plugin-prettier@5.4.0:
    resolution: {integrity: sha1-VNR0iQTljq8f/ibEv/pJhsp/lSs=}
    engines: {node: ^14.18.0 || >=16.0.0}
    peerDependencies:
      '@types/eslint': '>=8.0.0'
      eslint: '>=8.0.0'
      eslint-config-prettier: '>= 7.0.0 <10.0.0 || >=10.1.0'
      prettier: '>=3.0.0'
    peerDependenciesMeta:
      '@types/eslint':
        optional: true
      eslint-config-prettier:
        optional: true

  eslint-plugin-react-hooks@5.2.0:
    resolution: {integrity: sha1-G+AICQHmrDHOeXG+7T0+wKQj2eM=}
    engines: {node: '>=10'}
    peerDependencies:
      eslint: ^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0

  eslint-plugin-react-refresh@0.4.20:
    resolution: {integrity: sha1-O7+1yGN+KNGc40Q2hkReUC7NGLo=}
    peerDependencies:
      eslint: '>=8.40'

  eslint-plugin-react@7.37.5:
    resolution: {integrity: sha1-KXVRFHK92hsnKzTXeTNcmw6HcGU=}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7

  eslint-plugin-unused-imports@4.1.4:
    resolution: {integrity: sha1-Yt3HRGzL+ap7bx8LAKmAQjzaJzg=}
    peerDependencies:
      '@typescript-eslint/eslint-plugin': ^8.0.0-0 || ^7.0.0 || ^6.0.0 || ^5.0.0
      eslint: ^9.0.0 || ^8.0.0
    peerDependenciesMeta:
      '@typescript-eslint/eslint-plugin':
        optional: true

  eslint-scope@8.3.0:
    resolution: {integrity: sha1-EM06kY/91yL18/e1uD25sjyHNA0=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-visitor-keys@4.2.0:
    resolution: {integrity: sha1-aHussq+IT83aim59ZcYG9GoUzUU=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  eslint@9.27.0:
    resolution: {integrity: sha1-pYfTzVuES2jfeJiUQyOnAq/jiXk=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    hasBin: true
    peerDependencies:
      jiti: '*'
    peerDependenciesMeta:
      jiti:
        optional: true

  espree@10.3.0:
    resolution: {integrity: sha1-KSZ89bDLmHNbZeZLoH4O1J0e7Yo=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}

  esquery@1.6.0:
    resolution: {integrity: sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha1-eteWTWeauyi+5yzsY3WLHF0smSE=}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha1-LupSkHAvJquP5TcDcP+GyWXSESM=}
    engines: {node: '>=4.0'}

  estree-walker@3.0.3:
    resolution: {integrity: sha1-Z8PlSexAKkh7T8GT0ZU6UkdSNA0=}

  esutils@2.0.3:
    resolution: {integrity: sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=}
    engines: {node: '>=0.10.0'}

  eventemitter3@5.0.1:
    resolution: {integrity: sha1-U/X/0KSSrIAHIbtCxmuEHelkI8Q=}

  execa@8.0.1:
    resolution: {integrity: sha1-UfallDtYD5Y8PKnGMheW24zDm4w=}
    engines: {node: '>=16.17'}

  exifr@7.1.3:
    resolution: {integrity: sha1-9iGAEsNtu32EMiIBGyfwZf3bq28=}

  expect-type@1.2.1:
    resolution: {integrity: sha1-r3bYs1fPX6dsQcCdr7ecVJ519x8=}
    engines: {node: '>=12.0.0'}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=}

  fast-diff@1.3.0:
    resolution: {integrity: sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=}

  fast-glob@3.3.3:
    resolution: {integrity: sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=}

  fast-uri@3.0.6:
    resolution: {integrity: sha1-iPEwt3z66iN41Wv5cN6iElemh0g=}

  fastq@1.19.1:
    resolution: {integrity: sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=}

  fdir@6.4.4:
    resolution: {integrity: sha1-HPz4b4daiD4ZqPq1NiLP6ZLo0vk=}
    peerDependencies:
      picomatch: ^3 || ^4
    peerDependenciesMeta:
      picomatch:
        optional: true

  file-entry-cache@8.0.0:
    resolution: {integrity: sha1-d4e93PETG/+5JjbGlFe7wO3W2B8=}
    engines: {node: '>=16.0.0'}

  file-selector@2.1.2:
    resolution: {integrity: sha1-/nx+6eVQlS37yGPXOxTcdA196LQ=}
    engines: {node: '>= 12'}

  fill-range@7.1.1:
    resolution: {integrity: sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=}
    engines: {node: '>=8'}

  find-root@1.1.0:
    resolution: {integrity: sha1-q8/Iunb3CMQql7PWhbfpRQv7nOQ=}

  find-up@5.0.0:
    resolution: {integrity: sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=}
    engines: {node: '>=10'}

  find-up@7.0.0:
    resolution: {integrity: sha1-6N7BRV90942IitZb98oT3StOZvs=}
    engines: {node: '>=18'}

  flat-cache@4.0.1:
    resolution: {integrity: sha1-Ds45/LFO4BL0sEEL0z3ZwfAREnw=}
    engines: {node: '>=16'}

  flatted@3.3.3:
    resolution: {integrity: sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=}

  follow-redirects@1.15.9:
    resolution: {integrity: sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  for-each@0.3.5:
    resolution: {integrity: sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=}
    engines: {node: '>= 0.4'}

  form-data@4.0.2:
    resolution: {integrity: sha1-Ncq73TDDznPessQtPI0+2cpReUw=}
    engines: {node: '>= 6'}

  fraction.js@4.3.7:
    resolution: {integrity: sha1-BsoAhRV+Qv2n+ecm55/vxAaIQPc=}

  framer-motion@12.12.1:
    resolution: {integrity: sha1-t1YHnMBQvn+mrg0JOrCQP9PxXKE=}
    peerDependencies:
      '@emotion/is-prop-valid': '*'
      react: ^18.0.0 || ^19.0.0
      react-dom: ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@emotion/is-prop-valid':
        optional: true
      react:
        optional: true
      react-dom:
        optional: true

  fsevents@2.3.2:
    resolution: {integrity: sha1-ilJveLj99GI7cJ4Ll1xSwkwC/Ro=}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  fsevents@2.3.3:
    resolution: {integrity: sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=}

  function.prototype.name@1.1.8:
    resolution: {integrity: sha1-5o4d97JZpclJ7u+Vzb3lPt/6u3g=}
    engines: {node: '>= 0.4'}

  functions-have-names@1.2.3:
    resolution: {integrity: sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=}
    engines: {node: '>=6.9.0'}

  geotiff@2.1.3:
    resolution: {integrity: sha1-mT9A8qpqpl+x4EUdht0iyo5mkQw=}
    engines: {node: '>=10.19'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-east-asian-width@1.3.0:
    resolution: {integrity: sha1-IbQHHuWO0E7g22UzcbVbQpmHU4k=}
    engines: {node: '>=18'}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=}
    engines: {node: '>= 0.4'}

  get-nonce@1.0.1:
    resolution: {integrity: sha1-/fPwJ4Bzgg0s6UJsGPB0gbHgzfM=}
    engines: {node: '>=6'}

  get-proto@1.0.1:
    resolution: {integrity: sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=}
    engines: {node: '>= 0.4'}

  get-stream@8.0.1:
    resolution: {integrity: sha1-3vnf1xdCzXdUp3Ye1DdJon0C7KI=}
    engines: {node: '>=16'}

  get-symbol-description@1.1.0:
    resolution: {integrity: sha1-e91U4L7+j/yfO04gMiDZ8eiBtu4=}
    engines: {node: '>= 0.4'}

  git-raw-commits@4.0.0:
    resolution: {integrity: sha1-shL9K/+XJtJ8EoOhFX6ClJBZMoU=}
    engines: {node: '>=16'}
    hasBin: true

  glob-parent@5.1.2:
    resolution: {integrity: sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=}
    engines: {node: '>=10.13.0'}

  global-directory@4.0.1:
    resolution: {integrity: sha1-TXrHz9LLc/MExTuIEIkXSN9eNh4=}
    engines: {node: '>=18'}

  global-jsdom@26.0.0:
    resolution: {integrity: sha1-/N3UD4H/EVMeXOh+Gjd68BYBjpg=}
    engines: {node: '>=18'}
    peerDependencies:
      jsdom: '>=26 <27'

  globals@11.12.0:
    resolution: {integrity: sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=}
    engines: {node: '>=4'}

  globals@14.0.0:
    resolution: {integrity: sha1-iY10E8Kbq89rr+Vvyt3thYrack4=}
    engines: {node: '>=18'}

  globals@15.15.0:
    resolution: {integrity: sha1-fEdhKZ1BwysHVxWkzh7eeJf/cqg=}
    engines: {node: '>=18'}

  globalthis@1.0.4:
    resolution: {integrity: sha1-dDDtOpddl7+1m8zkH1yruvplEjY=}
    engines: {node: '>= 0.4'}

  gopd@1.2.0:
    resolution: {integrity: sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=}
    engines: {node: '>= 0.4'}

  graceful-fs@4.2.11:
    resolution: {integrity: sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=}

  graphemer@1.4.0:
    resolution: {integrity: sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=}

  has-bigints@1.1.0:
    resolution: {integrity: sha1-KGB+llrJZ+A80qLHCiY2oe2tSf4=}
    engines: {node: '>= 0.4'}

  has-flag@4.0.0:
    resolution: {integrity: sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=}
    engines: {node: '>=8'}

  has-property-descriptors@1.0.2:
    resolution: {integrity: sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=}

  has-proto@1.2.0:
    resolution: {integrity: sha1-XeWm6r2V/f/ZgYtDBV6AZeOf6dU=}
    engines: {node: '>= 0.4'}

  has-symbols@1.1.0:
    resolution: {integrity: sha1-/JxqeDoISVHQuXH+EBjegTcHozg=}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha1-AD6vkb563DcuhOxZ3DclLO24AAM=}
    engines: {node: '>= 0.4'}

  hoist-non-react-statics@3.3.2:
    resolution: {integrity: sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=}

  html-encoding-sniffer@4.0.0:
    resolution: {integrity: sha1-aW31KafP2CRGNp3FGT5ZCjc1tEg=}
    engines: {node: '>=18'}

  http-proxy-agent@7.0.2:
    resolution: {integrity: sha1-mosfJGhmwChQlIZYX2K48sGMJw4=}
    engines: {node: '>= 14'}

  https-proxy-agent@7.0.6:
    resolution: {integrity: sha1-2o3+rH2hMLBcK6S1nJts1mYRprk=}
    engines: {node: '>= 14'}

  human-signals@5.0.0:
    resolution: {integrity: sha1-QmZaKE+a4NreO6QevDfrS4UvOig=}
    engines: {node: '>=16.17.0'}

  husky@9.1.7:
    resolution: {integrity: sha1-1Go4A10QG0anBFaoUP9CATRMCy0=}
    engines: {node: '>=18'}
    hasBin: true

  iconv-lite@0.6.3:
    resolution: {integrity: sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=}
    engines: {node: '>=0.10.0'}

  ignore@5.3.2:
    resolution: {integrity: sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=}
    engines: {node: '>= 4'}

  ignore@7.0.4:
    resolution: {integrity: sha1-oSxw0PJgfFv1CPtlpAx18DfXoHg=}
    engines: {node: '>= 4'}

  immer@10.1.1:
    resolution: {integrity: sha1-IG80TqNy2OoXaJFUXuU8zAYtt7w=}

  import-fresh@3.3.1:
    resolution: {integrity: sha1-nOy1ZQPAraHydB271lRuSxO1fM8=}
    engines: {node: '>=6'}

  import-in-the-middle@1.13.2:
    resolution: {integrity: sha1-uNhzcIqxIZltpoQvp3QKxc1Df54=}

  import-meta-resolve@4.1.0:
    resolution: {integrity: sha1-+duL6tn6+mGtuBHbd6K/IsU5lwY=}

  imurmurhash@0.1.4:
    resolution: {integrity: sha1-khi5srkoojixPcT7a21XbyMUU+o=}
    engines: {node: '>=0.8.19'}

  indent-string@4.0.0:
    resolution: {integrity: sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=}
    engines: {node: '>=8'}

  ini@4.1.1:
    resolution: {integrity: sha1-2Vs9hDsekG5W1nR9VEeQT/UM56E=}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  internal-slot@1.1.0:
    resolution: {integrity: sha1-HqyRdilH0vcFa8g42T4TsulgSWE=}
    engines: {node: '>= 0.4'}

  is-array-buffer@3.0.5:
    resolution: {integrity: sha1-ZXQuHmh70sxmYlMGj9hwf+TUQoA=}
    engines: {node: '>= 0.4'}

  is-arrayish@0.2.1:
    resolution: {integrity: sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=}

  is-async-function@2.1.1:
    resolution: {integrity: sha1-PmkBjI4E5ztzh5PQIL/ohLn9NSM=}
    engines: {node: '>= 0.4'}

  is-bigint@1.1.0:
    resolution: {integrity: sha1-3aejRF31ekJYPbQihoLrp8QXBnI=}
    engines: {node: '>= 0.4'}

  is-boolean-object@1.2.2:
    resolution: {integrity: sha1-cGf0dwmAmjk8cf9bs+E12KkhXZ4=}
    engines: {node: '>= 0.4'}

  is-callable@1.2.7:
    resolution: {integrity: sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=}
    engines: {node: '>= 0.4'}

  is-core-module@2.16.1:
    resolution: {integrity: sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=}
    engines: {node: '>= 0.4'}

  is-data-view@1.0.2:
    resolution: {integrity: sha1-uuCkG5aImGwhiN2mZX5WuPnmO44=}
    engines: {node: '>= 0.4'}

  is-date-object@1.1.0:
    resolution: {integrity: sha1-rYVUGZb8eqiycpcB0ntzGfldgvc=}
    engines: {node: '>= 0.4'}

  is-extglob@2.1.1:
    resolution: {integrity: sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=}
    engines: {node: '>=0.10.0'}

  is-finalizationregistry@1.1.1:
    resolution: {integrity: sha1-7v3NxslN3QZ02chYh7+T+USpfJA=}
    engines: {node: '>= 0.4'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=}
    engines: {node: '>=8'}

  is-fullwidth-code-point@4.0.0:
    resolution: {integrity: sha1-+uMWfHKedGP4RhzlErCApJJoqog=}
    engines: {node: '>=12'}

  is-fullwidth-code-point@5.0.0:
    resolution: {integrity: sha1-lgnvztfC+X2ntgFF70gceHx7pwQ=}
    engines: {node: '>=18'}

  is-generator-function@1.1.0:
    resolution: {integrity: sha1-vz7tqTEgE5T1e126KAD5GiODCco=}
    engines: {node: '>= 0.4'}

  is-glob@4.0.3:
    resolution: {integrity: sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=}
    engines: {node: '>=0.10.0'}

  is-map@2.0.3:
    resolution: {integrity: sha1-7elrf+HicLPERl46RlZYdkkm1i4=}
    engines: {node: '>= 0.4'}

  is-network-error@1.1.0:
    resolution: {integrity: sha1-0mp2DjdwIm0RwWkFLyZqSAPZyZc=}
    engines: {node: '>=16'}

  is-number-object@1.1.1:
    resolution: {integrity: sha1-FEsh6VobwUggXcwoFKkTTsQbJUE=}
    engines: {node: '>= 0.4'}

  is-number@7.0.0:
    resolution: {integrity: sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=}
    engines: {node: '>=0.12.0'}

  is-obj@2.0.0:
    resolution: {integrity: sha1-Rz+wXZc3BeP9liBUUBjKjiLvSYI=}
    engines: {node: '>=8'}

  is-potential-custom-element-name@1.0.1:
    resolution: {integrity: sha1-Fx7W8Z46xVQ5Tt94yqBXhKRb67U=}

  is-regex@1.2.1:
    resolution: {integrity: sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=}
    engines: {node: '>= 0.4'}

  is-set@2.0.3:
    resolution: {integrity: sha1-irIJ6kJGCBQTct7W4MsgDvHZ0B0=}
    engines: {node: '>= 0.4'}

  is-shared-array-buffer@1.0.4:
    resolution: {integrity: sha1-m2eES9m38ka6BwjDqT40Jpx3T28=}
    engines: {node: '>= 0.4'}

  is-stream@3.0.0:
    resolution: {integrity: sha1-5r/XqmvvafT0cs6btoHj5XtDGaw=}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  is-string@1.1.1:
    resolution: {integrity: sha1-kuo/PVxbbgOcqGd+WsjQfqdzy7k=}
    engines: {node: '>= 0.4'}

  is-symbol@1.1.1:
    resolution: {integrity: sha1-9HdhJ59TLisFpwJKdQbbvtrNBjQ=}
    engines: {node: '>= 0.4'}

  is-text-path@2.0.0:
    resolution: {integrity: sha1-skhOK3IKYz/rLoW2fcGT/3LHVjY=}
    engines: {node: '>=8'}

  is-typed-array@1.1.15:
    resolution: {integrity: sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=}
    engines: {node: '>= 0.4'}

  is-weakmap@2.0.2:
    resolution: {integrity: sha1-v3JhXWSd/l9pkHnFS4PkfRrhnP0=}
    engines: {node: '>= 0.4'}

  is-weakref@1.1.1:
    resolution: {integrity: sha1-7qQwGCvo1kF0vZa/+8RvIb8/kpM=}
    engines: {node: '>= 0.4'}

  is-weakset@2.0.4:
    resolution: {integrity: sha1-yfXesLwZBsbW8QJ/KE3fRZJJ2so=}
    engines: {node: '>= 0.4'}

  isarray@2.0.5:
    resolution: {integrity: sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=}

  isexe@2.0.0:
    resolution: {integrity: sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=}

  iterator.prototype@1.1.5:
    resolution: {integrity: sha1-EslZop3jLeCqO7u4AfTXdwZtrjk=}
    engines: {node: '>= 0.4'}

  jiti@2.4.2:
    resolution: {integrity: sha1-0Zt3Muu2EWsG4gONp0pVNm+u9WA=}
    hasBin: true

  js-tokens@4.0.0:
    resolution: {integrity: sha1-GSA/tZmR35jjoocFDUZHzerzJJk=}

  js-yaml@4.1.0:
    resolution: {integrity: sha1-wftl+PUBeQHN0slRhkuhhFihBgI=}
    hasBin: true

  jsdom@26.1.0:
    resolution: {integrity: sha1-q18cHK/AS9h4clSQl06l6L8McrM=}
    engines: {node: '>=18'}
    peerDependencies:
      canvas: ^3.0.0
    peerDependenciesMeta:
      canvas:
        optional: true

  jsesc@3.1.0:
    resolution: {integrity: sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=}
    engines: {node: '>=6'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=}

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha1-afaofZUTq4u4/mO9sJecRI5oRmA=}

  json-schema-traverse@1.0.0:
    resolution: {integrity: sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=}

  json-stringify-pretty-compact@4.0.0:
    resolution: {integrity: sha1-z0hEdwvd7jy4mmFw/ksA7uXb8dQ=}

  json5@2.2.3:
    resolution: {integrity: sha1-eM1vGhm9wStz21rQxh79ZsHikoM=}
    engines: {node: '>=6'}
    hasBin: true

  jsonparse@1.3.1:
    resolution: {integrity: sha1-P02uSpH6wxX3EGL4UhzCOfE2YoA=}
    engines: {'0': node >= 0.2.0}

  jsx-ast-utils@3.3.5:
    resolution: {integrity: sha1-R2a9BajioRryIr7NGeFVdeUqhTo=}
    engines: {node: '>=4.0'}

  jwt-decode@4.0.0:
    resolution: {integrity: sha1-InA1JCX9QTeFsvrxH251XFFRvUs=}
    engines: {node: '>=18'}

  keyv@4.5.4:
    resolution: {integrity: sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=}

  lerc@3.0.0:
    resolution: {integrity: sha1-NvNvvUukbwq/SDN5n/8ufWhl9cs=}

  levn@0.4.1:
    resolution: {integrity: sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=}
    engines: {node: '>= 0.8.0'}

  lightningcss-darwin-arm64@1.30.1:
    resolution: {integrity: sha1-PUfOXiIblWfHA5UO3yUpyko3AK4=}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [darwin]

  lightningcss-darwin-x64@1.30.1:
    resolution: {integrity: sha1-6BEF0/1jMIYMFf6GD2TTnP9fvSI=}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [darwin]

  lightningcss-freebsd-x64@1.30.1:
    resolution: {integrity: sha1-oOcyAxCD/51iXF2wIdCesIWvi+Q=}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [freebsd]

  lightningcss-linux-arm-gnueabihf@1.30.1:
    resolution: {integrity: sha1-H17MpglVKN22SfkwS6JWDHJHSQg=}
    engines: {node: '>= 12.0.0'}
    cpu: [arm]
    os: [linux]

  lightningcss-linux-arm64-gnu@1.30.1:
    resolution: {integrity: sha1-7ud5lyYQO///HoiZPfcm9pEewAk=}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [glibc]

  lightningcss-linux-arm64-musl@1.30.1:
    resolution: {integrity: sha1-8uS1P0KJL+7vj2IMu4iffAZKff4=}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]
    libc: [musl]

  lightningcss-linux-x64-gnu@1.30.1:
    resolution: {integrity: sha1-L8cJYiS8AA67l+6pSuokjFsOsVc=}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [glibc]

  lightningcss-linux-x64-musl@1.30.1:
    resolution: {integrity: sha1-ZtyisVn9gZ6oMsRIldB+WzHXXyY=}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]
    libc: [musl]

  lightningcss-win32-arm64-msvc@1.30.1:
    resolution: {integrity: sha1-fYEQoZ18LSK/3y8ruL5o59G2kDk=}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [win32]

  lightningcss-win32-x64-msvc@1.30.1:
    resolution: {integrity: sha1-/X3QCOqYSUuF0ktL6gFnk/Lg41I=}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [win32]

  lightningcss@1.30.1:
    resolution: {integrity: sha1-eOl5wtWVv8uQ0qjA62Mv5sW/7V0=}
    engines: {node: '>= 12.0.0'}

  lilconfig@3.1.3:
    resolution: {integrity: sha1-obz9Ylf5WFv1rhTO7rt7VZAl5MQ=}
    engines: {node: '>=14'}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha1-7KKE910pZQeTCdwK2SVauy68FjI=}

  lint-staged@15.5.2:
    resolution: {integrity: sha1-vv8Cj9BoH32yb/u2cFCiHtTQWaM=}
    engines: {node: '>=18.12.0'}
    hasBin: true

  listr2@8.3.3:
    resolution: {integrity: sha1-gV/I9zgmD/IgmBv56Gaz4R6BIb8=}
    engines: {node: '>=18.0.0'}

  locate-path@6.0.0:
    resolution: {integrity: sha1-VTIeswn+u8WcSAHZMackUqaB0oY=}
    engines: {node: '>=10'}

  locate-path@7.2.0:
    resolution: {integrity: sha1-acsXeb2Qs1qx53Hh8viaICwqioo=}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  lodash.camelcase@4.3.0:
    resolution: {integrity: sha1-soqmKIorn8ZRA1x3EfZathkDMaY=}

  lodash.isplainobject@4.0.6:
    resolution: {integrity: sha1-fFJqUtibRcRcxpC4gWO+BJf1UMs=}

  lodash.kebabcase@4.1.1:
    resolution: {integrity: sha1-hImxyw0p/4gZXM7KRI/21swpXDY=}

  lodash.merge@4.6.2:
    resolution: {integrity: sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=}

  lodash.mergewith@4.6.2:
    resolution: {integrity: sha1-YXEh+JrFX1kEfHrsHM1mVMZZD1U=}

  lodash.snakecase@4.1.1:
    resolution: {integrity: sha1-OdcUo1NXFHg3rv1ktdy7Fr7Nj40=}

  lodash.startcase@4.4.0:
    resolution: {integrity: sha1-lDbjTtJgk+1/+uGTYUQ1CRXZrdg=}

  lodash.uniq@4.5.0:
    resolution: {integrity: sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=}

  lodash.upperfirst@4.3.1:
    resolution: {integrity: sha1-E2Xt9DFIBIHvDRxolXpe2Z1J984=}

  lodash@4.17.21:
    resolution: {integrity: sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=}

  log-update@6.1.0:
    resolution: {integrity: sha1-GgT/OBZvlGR64a9WL0vWoVsbfNQ=}
    engines: {node: '>=18'}

  long@5.3.2:
    resolution: {integrity: sha1-HYRGMJWZkmLX17f4v9SozFUWf4M=}

  loose-envify@1.4.0:
    resolution: {integrity: sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=}
    hasBin: true

  loupe@3.1.3:
    resolution: {integrity: sha1-BCqPeYbXfz0PmO95kKKy/vGLD9I=}

  lru-cache@10.4.3:
    resolution: {integrity: sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=}

  lru-cache@5.1.1:
    resolution: {integrity: sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=}

  lucide-react@0.475.0:
    resolution: {integrity: sha1-S3tiwCTxU+5LUqag8z+ecvBxVvA=}
    peerDependencies:
      react: ^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0

  lucide-react@0.476.0:
    resolution: {integrity: sha1-ozNUgy6oWyw3ZPrwnYXJqTMvf2A=}
    peerDependencies:
      react: ^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0

  lz-string@1.5.0:
    resolution: {integrity: sha1-watQ93iHtxJiEgG6n9Tjpu0JmUE=}
    hasBin: true

  magic-string@0.30.17:
    resolution: {integrity: sha1-RQpElnPSRg5bvPupphkWoXFMdFM=}

  mapbox-to-css-font@3.2.0:
    resolution: {integrity: sha1-iZeB8iTPb/4BmNZoLlU7Z01NX3I=}

  masonic@4.1.0:
    resolution: {integrity: sha1-3qv5SQJCCxOZIqOGShELj1oMwd8=}
    peerDependencies:
      react: '>=16.8'

  math-intrinsics@1.1.0:
    resolution: {integrity: sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=}
    engines: {node: '>= 0.4'}

  memoize-one@6.0.0:
    resolution: {integrity: sha1-slkbhx7YKUiu5HJ9xqvO7qyMEEU=}

  meow@12.1.1:
    resolution: {integrity: sha1-5Vjd26sSR3tpsumicowyfxkbrOY=}
    engines: {node: '>=16.10'}

  merge-stream@2.0.0:
    resolution: {integrity: sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=}

  merge2@1.4.1:
    resolution: {integrity: sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=}
    engines: {node: '>= 8'}

  micromatch@4.0.8:
    resolution: {integrity: sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha1-u6vNwChZ9JhzAchW4zh85exDv3A=}
    engines: {node: '>= 0.6'}

  mime-match@1.0.2:
    resolution: {integrity: sha1-P4fDHprxpf1IX7nbE0Qosju7e6g=}

  mime-types@2.1.35:
    resolution: {integrity: sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=}
    engines: {node: '>= 0.6'}

  mimic-fn@4.0.0:
    resolution: {integrity: sha1-YKkFUNXLCyOcymXYk7GlOymHHsw=}
    engines: {node: '>=12'}

  mimic-function@5.0.1:
    resolution: {integrity: sha1-rL4rM0n5m53qyn+3Dki4PpTmcHY=}
    engines: {node: '>=18'}

  min-indent@1.0.1:
    resolution: {integrity: sha1-pj9oFnOzBXH76LwlaGrnRu76mGk=}
    engines: {node: '>=4'}

  minimatch@3.1.2:
    resolution: {integrity: sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=}

  minimatch@9.0.5:
    resolution: {integrity: sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=}

  minipass@7.1.2:
    resolution: {integrity: sha1-k6libOXl5mvU24aEnnUV6SNApwc=}
    engines: {node: '>=16 || 14 >=14.17'}

  minizlib@3.0.2:
    resolution: {integrity: sha1-8z1jjrJ59mRDmqONxfkWB0aMtXQ=}
    engines: {node: '>= 18'}

  mkdirp@3.0.1:
    resolution: {integrity: sha1-5E5MVgf7J5wWgkFxPMbg/qmty1A=}
    engines: {node: '>=10'}
    hasBin: true

  module-details-from-path@1.0.4:
    resolution: {integrity: sha1-tmL9zZP2yD0/JSidoM6ByNloW5Q=}

  motion-dom@12.12.1:
    resolution: {integrity: sha1-EvUMd4yild4zfijztJN9qG6QmOo=}

  motion-utils@12.12.1:
    resolution: {integrity: sha1-Y+KHUTJcudHNaE88JzpXACKwAQ4=}

  ms@2.1.3:
    resolution: {integrity: sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=}

  namespace-emitter@2.0.1:
    resolution: {integrity: sha1-l41RNhxhMTtOa4z284U9CN+isXw=}

  nanoid@3.3.11:
    resolution: {integrity: sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  nanoid@5.1.5:
    resolution: {integrity: sha1-91l/nZBU602pVIzdU8pw8XkOh94=}
    engines: {node: ^18 || >=20}
    hasBin: true

  natural-compare@1.4.0:
    resolution: {integrity: sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=}

  node-releases@2.0.19:
    resolution: {integrity: sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=}

  normalize-range@0.1.2:
    resolution: {integrity: sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=}
    engines: {node: '>=0.10.0'}

  npm-run-path@5.3.0:
    resolution: {integrity: sha1-4jNT0Ou5MX8XTpNBfkpNgtAknp8=}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  nwsapi@2.2.20:
    resolution: {integrity: sha1-IuUyU8Yeew5+k870LIkRVLzKEe8=}

  object-assign@4.1.1:
    resolution: {integrity: sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=}
    engines: {node: '>=0.10.0'}

  object-inspect@1.13.4:
    resolution: {integrity: sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=}
    engines: {node: '>= 0.4'}

  object-keys@1.1.1:
    resolution: {integrity: sha1-HEfyct8nfzsdrwYWd9nILiMixg4=}
    engines: {node: '>= 0.4'}

  object.assign@4.1.7:
    resolution: {integrity: sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=}
    engines: {node: '>= 0.4'}

  object.entries@1.1.9:
    resolution: {integrity: sha1-5HcKahREr7Yb05+YQBi1vt4l+LM=}
    engines: {node: '>= 0.4'}

  object.fromentries@2.0.8:
    resolution: {integrity: sha1-9xldipuXvZXLwZmeqTns0aKwDGU=}
    engines: {node: '>= 0.4'}

  object.values@1.2.1:
    resolution: {integrity: sha1-3u1SClCAn/f3Wnz9S8ZMegOMYhY=}
    engines: {node: '>= 0.4'}

  ol-mapbox-style@12.6.1:
    resolution: {integrity: sha1-n3kAE4V7Rq54sxNU8ebY0ft3lkA=}
    peerDependencies:
      ol: '*'

  ol@10.5.0:
    resolution: {integrity: sha1-WDHcVf5etaCctBk8konBtaCg8Mo=}

  onetime@6.0.0:
    resolution: {integrity: sha1-fCTBjtH9LpvKS9JoBqM2E8d9NLQ=}
    engines: {node: '>=12'}

  onetime@7.0.0:
    resolution: {integrity: sha1-nxbJLYye9RIOOs2d2ZV8zuzBq2A=}
    engines: {node: '>=18'}

  optionator@0.9.4:
    resolution: {integrity: sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=}
    engines: {node: '>= 0.8.0'}

  own-keys@1.0.1:
    resolution: {integrity: sha1-5ABpEKK/kTWFKJZ27r1vOQz1E1g=}
    engines: {node: '>= 0.4'}

  p-limit@3.1.0:
    resolution: {integrity: sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=}
    engines: {node: '>=10'}

  p-limit@4.0.0:
    resolution: {integrity: sha1-kUr2VE7TK/pUZwsGHK/L0EmEtkQ=}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  p-locate@5.0.0:
    resolution: {integrity: sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=}
    engines: {node: '>=10'}

  p-locate@6.0.0:
    resolution: {integrity: sha1-PamknUk0uQEIncozAvpl3FoFwE8=}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  p-queue@8.1.0:
    resolution: {integrity: sha1-1xkpJJhosQsW+IXYqCvurzXTInk=}
    engines: {node: '>=18'}

  p-retry@6.2.1:
    resolution: {integrity: sha1-gYKPjcYcbvWoAFhUkVcsyYknA68=}
    engines: {node: '>=16.17'}

  p-timeout@6.1.4:
    resolution: {integrity: sha1-QY4fTdgz+pai4/UyVH3Sq9sI28I=}
    engines: {node: '>=14.16'}

  pako@2.1.0:
    resolution: {integrity: sha1-JmzDf5jH2INUXREzXAD71AYsmoY=}

  parent-module@1.0.1:
    resolution: {integrity: sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=}
    engines: {node: '>=6'}

  parse-headers@2.0.6:
    resolution: {integrity: sha1-eUDwq+X+Zd8t0l1M6IAMs1tJ0Bw=}

  parse-json@5.2.0:
    resolution: {integrity: sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=}
    engines: {node: '>=8'}

  parse5@7.3.0:
    resolution: {integrity: sha1-1+Ik+nI5nHoXUJn0X8KtAksF7AU=}

  path-exists@4.0.0:
    resolution: {integrity: sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=}
    engines: {node: '>=8'}

  path-exists@5.0.0:
    resolution: {integrity: sha1-pqrZSJIAsh+rMeSc8JJ35RFvuec=}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  path-key@3.1.1:
    resolution: {integrity: sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=}
    engines: {node: '>=8'}

  path-key@4.0.0:
    resolution: {integrity: sha1-KVWI3DruZBVPh3rbnXgLgcVUvxg=}
    engines: {node: '>=12'}

  path-parse@1.0.7:
    resolution: {integrity: sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=}

  path-type@4.0.0:
    resolution: {integrity: sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=}
    engines: {node: '>=8'}

  pathe@2.0.3:
    resolution: {integrity: sha1-PsvsVUIWhbcKnahyss/z4cvtFxY=}

  pathval@2.0.0:
    resolution: {integrity: sha1-fiVQtCJgHU9rjibxMBvI8Vp0GiU=}
    engines: {node: '>= 14.16'}

  pbf@4.0.1:
    resolution: {integrity: sha1-rZAV4CKyNdzb4F/EaKmsrfSD8NQ=}
    hasBin: true

  picocolors@1.1.1:
    resolution: {integrity: sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=}

  picomatch@2.3.1:
    resolution: {integrity: sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=}
    engines: {node: '>=8.6'}

  picomatch@4.0.2:
    resolution: {integrity: sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=}
    engines: {node: '>=12'}

  pidtree@0.6.0:
    resolution: {integrity: sha1-kK17bULVhB5p4KJBnvOPiIOqBXw=}
    engines: {node: '>=0.10'}
    hasBin: true

  playwright-core@1.52.0:
    resolution: {integrity: sha1-I48fDD7dTrugQ0zj9EAZADGaPco=}
    engines: {node: '>=18'}
    hasBin: true

  playwright@1.52.0:
    resolution: {integrity: sha1-JsuaYzRmUeHFTIgFrP2FaDFz1L0=}
    engines: {node: '>=18'}
    hasBin: true

  possible-typed-array-names@1.1.0:
    resolution: {integrity: sha1-k+NYK8DlQmWG2dB7ee5A/IQd5K4=}
    engines: {node: '>= 0.4'}

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=}

  postcss@8.5.3:
    resolution: {integrity: sha1-FGO28cf7Fv4lhzbLopot41I36vs=}
    engines: {node: ^10 || ^12 || >=14}

  preact@10.26.6:
    resolution: {integrity: sha1-OIljzEqhX86v1lwX++3cOV/bDOs=}

  prelude-ls@1.2.1:
    resolution: {integrity: sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=}
    engines: {node: '>= 0.8.0'}

  prettier-linter-helpers@1.0.0:
    resolution: {integrity: sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=}
    engines: {node: '>=6.0.0'}

  prettier@3.5.3:
    resolution: {integrity: sha1-T8LODWV+egLmAlSfBTsjnLff4bU=}
    engines: {node: '>=14'}
    hasBin: true

  pretty-format@27.5.1:
    resolution: {integrity: sha1-IYGHn96lGnpYUfs52SD6pj8B2I4=}
    engines: {node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0}

  prop-types@15.8.1:
    resolution: {integrity: sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=}

  protobufjs@7.4.0:
    resolution: {integrity: sha1-fv4yTOmzthyCquXegQ0oe8CKJIo=}
    engines: {node: '>=12.0.0'}

  protocol-buffers-schema@3.6.0:
    resolution: {integrity: sha1-d7x1pIsv8ULBrVtbkMlM0Pou/QM=}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=}

  punycode@2.3.1:
    resolution: {integrity: sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=}
    engines: {node: '>=6'}

  queue-microtask@1.2.3:
    resolution: {integrity: sha1-SSkii7xyTfrEPg77BYyve2z7YkM=}

  quick-lru@6.1.2:
    resolution: {integrity: sha1-6akFJBCGKb41KH0Lhk561s6zZZ4=}
    engines: {node: '>=12'}

  quickselect@3.0.0:
    resolution: {integrity: sha1-o3/JU4Z9VvCVogrHHG0nBj0t5gM=}

  raf-schd@4.0.3:
    resolution: {integrity: sha1-XWw070b4sqDogKj823Q+/Fv9vBo=}

  rbush@4.0.1:
    resolution: {integrity: sha1-H1WvpkqXj3G/npqZvBT/hPPLDW0=}

  react-day-picker@8.10.1:
    resolution: {integrity: sha1-R2LsKYhlkZuT7Am6aWIVgINbjoA=}
    peerDependencies:
      date-fns: ^2.28.0 || ^3.0.0
      react: ^16.8.0 || ^17.0.0 || ^18.0.0

  react-dom@19.1.0:
    resolution: {integrity: sha1-EzVY3so3+h1oJwjfiQSyUYZ5NiM=}
    peerDependencies:
      react: ^19.1.0

  react-dropzone@14.3.8:
    resolution: {integrity: sha1-p+qxGPikUv4/ixYtZEVOgbqDBYI=}
    engines: {node: '>= 10.13'}
    peerDependencies:
      react: '>= 16.8 || 18.0.0'

  react-hook-form@7.56.4:
    resolution: {integrity: sha1-Jz9V1D+ySs4+K5QJ0NUyYwbXkF4=}
    engines: {node: '>=18.0.0'}
    peerDependencies:
      react: ^16.8.0 || ^17 || ^18 || ^19

  react-icons@5.5.0:
    resolution: {integrity: sha1-iqJdNUP/hCMWhdMzEWTAApnN+vI=}
    peerDependencies:
      react: '*'

  react-is@16.13.1:
    resolution: {integrity: sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=}

  react-is@17.0.2:
    resolution: {integrity: sha1-5pHUqOnHiTZWVVOas3J2Kw77VPA=}

  react-is@19.1.0:
    resolution: {integrity: sha1-gFvOMhVGt+FMCEmJx3AiNRu90Rs=}

  react-layout-masonry@1.2.0:
    resolution: {integrity: sha1-Z15AMsEXFaW0faLejlxZug/XKhI=}
    peerDependencies:
      react: ^18 || ^19

  react-photo-album@3.1.0:
    resolution: {integrity: sha1-kMpbEWNBVDztMCTFgOvrBjlMHP4=}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/react': ^18 || ^19
      react: ^18 || ^19
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-refresh@0.17.0:
    resolution: {integrity: sha1-t+V5w2V/I9BOzL5K0uWKjtUeflM=}
    engines: {node: '>=0.10.0'}

  react-remove-scroll-bar@2.3.8:
    resolution: {integrity: sha1-mcIPkI7kZ7OFtoo0abSj51ABIiM=}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-remove-scroll@2.7.0:
    resolution: {integrity: sha1-Ckg0F/6ZGazT5FnOkgCGQM+Jhmk=}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-responsive-masonry@2.7.1:
    resolution: {integrity: sha1-0ntzT+V5A0zyavEvQuQvKaMywLM=}

  react-router-dom@6.30.1:
    resolution: {integrity: sha1-2iWAwnLdthMl5DVHhWa+lWOkojc=}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      react: '>=16.8'
      react-dom: '>=16.8'

  react-router@6.30.1:
    resolution: {integrity: sha1-7LO4g8m6bb9dMZ3byZZ0f0q59MM=}
    engines: {node: '>=14.0.0'}
    peerDependencies:
      react: '>=16.8'

  react-style-singleton@2.2.3:
    resolution: {integrity: sha1-QmVgi+aaTXDP4wR/LGyIssOs44g=}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-transition-group@4.4.5:
    resolution: {integrity: sha1-5T1OPzNE2oUhSJ+++PJYHUK+zdE=}
    peerDependencies:
      react: '>=16.6.0'
      react-dom: '>=16.6.0'

  react@19.1.0:
    resolution: {integrity: sha1-kmhktsSNp2J/AEeV1szlDpB5O3U=}
    engines: {node: '>=0.10.0'}

  redent@3.0.0:
    resolution: {integrity: sha1-5Ve3mYMWu1PJ8fVvpiY1LGljBZ8=}
    engines: {node: '>=8'}

  reflect.getprototypeof@1.0.10:
    resolution: {integrity: sha1-xikhnnijMW2LYEx2XvaJlpZOe/k=}
    engines: {node: '>= 0.4'}

  regexp.prototype.flags@1.5.4:
    resolution: {integrity: sha1-GtbGLUSiWQB+VbOXDgD3Ru+8qhk=}
    engines: {node: '>= 0.4'}

  remove-accents@0.4.4:
    resolution: {integrity: sha1-c3BKv32uN2QpXUddK2r6xOoj5Nk=}

  require-directory@2.1.1:
    resolution: {integrity: sha1-jGStX9MNqxyXbiNE/+f3kqam30I=}
    engines: {node: '>=0.10.0'}

  require-from-string@2.0.2:
    resolution: {integrity: sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=}
    engines: {node: '>=0.10.0'}

  require-in-the-middle@7.5.2:
    resolution: {integrity: sha1-3CWxSK/61C5XDPDkG6MNwA8XA+w=}
    engines: {node: '>=8.6.0'}

  resolve-from@4.0.0:
    resolution: {integrity: sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=}
    engines: {node: '>=4'}

  resolve-from@5.0.0:
    resolution: {integrity: sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=}
    engines: {node: '>=8'}

  resolve-protobuf-schema@2.1.0:
    resolution: {integrity: sha1-nKmp5pzxkrva8QBuwZc5SKpKN1g=}

  resolve@1.22.10:
    resolution: {integrity: sha1-tmPoP/sJu/I4aURza6roAwKbizk=}
    engines: {node: '>= 0.4'}
    hasBin: true

  resolve@2.0.0-next.5:
    resolution: {integrity: sha1-aw7DEH5nHlK2jNBo7zJxc7kNwDw=}
    hasBin: true

  restore-cursor@5.1.0:
    resolution: {integrity: sha1-B2bZVpnvrLFBUJk/VbrwlT6h6+c=}
    engines: {node: '>=18'}

  retry@0.13.1:
    resolution: {integrity: sha1-GFsVh6z2eRnWOzVzSeA1N7JIRlg=}
    engines: {node: '>= 4'}

  reusify@1.1.0:
    resolution: {integrity: sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rfdc@1.4.1:
    resolution: {integrity: sha1-d492xPtzHZNBTo+SX77PZMzn9so=}

  rollup@4.41.0:
    resolution: {integrity: sha1-F0doNdKWd1nj/+vlgj7RX8S30T4=}
    engines: {node: '>=18.0.0', npm: '>=8.0.0'}
    hasBin: true

  rrweb-cssom@0.8.0:
    resolution: {integrity: sha1-MCHRtDUvvzthSq7tC8DVc5q+C8I=}

  run-parallel@1.2.0:
    resolution: {integrity: sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=}

  rw@1.3.3:
    resolution: {integrity: sha1-P4Yt+pGrdmsUiF700BEkv9oHT7Q=}

  safe-array-concat@1.1.3:
    resolution: {integrity: sha1-yeVOxPYDsLu45+UAel7nrs0VOMM=}
    engines: {node: '>=0.4'}

  safe-push-apply@1.0.0:
    resolution: {integrity: sha1-AYUOmBwWAtOYyFCB82Dk5tA9J/U=}
    engines: {node: '>= 0.4'}

  safe-regex-test@1.1.0:
    resolution: {integrity: sha1-f4fftnoxUHguqvGFg/9dFxGsEME=}
    engines: {node: '>= 0.4'}

  safer-buffer@2.1.2:
    resolution: {integrity: sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=}

  saxes@6.0.0:
    resolution: {integrity: sha1-/ltKR2jfTxSiAbG6amXB89mYjMU=}
    engines: {node: '>=v12.22.7'}

  scheduler@0.26.0:
    resolution: {integrity: sha1-TOiowqIJXxPqEb+aRFvlDFVdYzc=}

  semver@6.3.1:
    resolution: {integrity: sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=}
    hasBin: true

  semver@7.7.2:
    resolution: {integrity: sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=}
    engines: {node: '>=10'}
    hasBin: true

  set-function-length@1.2.2:
    resolution: {integrity: sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=}
    engines: {node: '>= 0.4'}

  set-function-name@2.0.2:
    resolution: {integrity: sha1-FqcFxaDcL15jjKltiozU4cK5CYU=}
    engines: {node: '>= 0.4'}

  set-proto@1.0.0:
    resolution: {integrity: sha1-B2Dbz/MLLX6AH9bhmYPlbaM3Vl4=}
    engines: {node: '>= 0.4'}

  shallow-equal@3.1.0:
    resolution: {integrity: sha1-56VLrGKcfySO/2wvW2MSK6QyC+w=}

  shebang-command@2.0.0:
    resolution: {integrity: sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=}
    engines: {node: '>=8'}

  shimmer@1.2.1:
    resolution: {integrity: sha1-YQhZ994ye1h+/r9QH7QxF/mv8zc=}

  side-channel-list@1.0.0:
    resolution: {integrity: sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=}
    engines: {node: '>= 0.4'}

  side-channel-map@1.0.1:
    resolution: {integrity: sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=}
    engines: {node: '>= 0.4'}

  side-channel-weakmap@1.0.2:
    resolution: {integrity: sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=}
    engines: {node: '>= 0.4'}

  side-channel@1.1.0:
    resolution: {integrity: sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=}
    engines: {node: '>= 0.4'}

  siginfo@2.0.0:
    resolution: {integrity: sha1-MudscLeXJOO7Vny51UPrhYzPrzA=}

  signal-exit@4.1.0:
    resolution: {integrity: sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=}
    engines: {node: '>=14'}

  slice-ansi@5.0.0:
    resolution: {integrity: sha1-tzBjxXqpb5zYgWVLFSlNldKFxCo=}
    engines: {node: '>=12'}

  slice-ansi@7.1.0:
    resolution: {integrity: sha1-zWtGVeKYqNG96wQlCkMwlLNHuak=}
    engines: {node: '>=18'}

  source-map-js@1.2.1:
    resolution: {integrity: sha1-HOVlD93YerwJnto33P8CTCZnrkY=}
    engines: {node: '>=0.10.0'}

  source-map@0.5.7:
    resolution: {integrity: sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=}
    engines: {node: '>=0.10.0'}

  split2@4.2.0:
    resolution: {integrity: sha1-ycWSCQTRSLqwufZxRfJFqGqtv6Q=}
    engines: {node: '>= 10.x'}

  stackback@0.0.2:
    resolution: {integrity: sha1-Gsig2Ug4SNFpXkGLbQMaPDzmjjs=}

  std-env@3.9.0:
    resolution: {integrity: sha1-Gm9yQ7M53KTJ/VXhx1BMd+8j6PE=}

  string-argv@0.3.2:
    resolution: {integrity: sha1-K20O8ktlYnTZV9VOCku/YVPcArY=}
    engines: {node: '>=0.6.19'}

  string-width@4.2.3:
    resolution: {integrity: sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=}
    engines: {node: '>=8'}

  string-width@7.2.0:
    resolution: {integrity: sha1-tbuOIWXOJ11NQ0dt0nAK2Qkdttw=}
    engines: {node: '>=18'}

  string.prototype.matchall@4.0.12:
    resolution: {integrity: sha1-bIh0DkmtSVaxMyqRHpSVg6J11MA=}
    engines: {node: '>= 0.4'}

  string.prototype.repeat@1.0.0:
    resolution: {integrity: sha1-6Qhy7gMIspQ1qiYnX24bdi2u4Bo=}

  string.prototype.trim@1.2.10:
    resolution: {integrity: sha1-QLLdXulMlZtNz7HWXOcukNpIDIE=}
    engines: {node: '>= 0.4'}

  string.prototype.trimend@1.0.9:
    resolution: {integrity: sha1-YuJzEnLNKFBBs2WWBU6fZlabaUI=}
    engines: {node: '>= 0.4'}

  string.prototype.trimstart@1.0.8:
    resolution: {integrity: sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=}
    engines: {node: '>= 0.4'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=}
    engines: {node: '>=12'}

  strip-final-newline@3.0.0:
    resolution: {integrity: sha1-UolMMT+/8xiDUoCu1g/3Hr8SuP0=}
    engines: {node: '>=12'}

  strip-indent@3.0.0:
    resolution: {integrity: sha1-wy4c7pQLazQyx3G8LFS8znPNMAE=}
    engines: {node: '>=8'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=}
    engines: {node: '>=8'}

  stylis@4.2.0:
    resolution: {integrity: sha1-edruAgiWTI/mlaQvz/ysYzohGlE=}

  supports-color@7.2.0:
    resolution: {integrity: sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=}
    engines: {node: '>=8'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha1-btpL00SjyUrqN21MwxvHcxEDngk=}
    engines: {node: '>= 0.4'}

  swiper@11.2.7:
    resolution: {integrity: sha1-llfkZwR88Ji2yaedGGS8aBDS5Ps=}
    engines: {node: '>= 4.7.0'}

  symbol-tree@3.2.4:
    resolution: {integrity: sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=}

  synckit@0.11.6:
    resolution: {integrity: sha1-50Kgwnu8H7yW8gEHcFIQFcyn7Vw=}
    engines: {node: ^14.18.0 || >=16.0.0}

  tailwind-merge@3.3.0:
    resolution: {integrity: sha1-2btPKYgB72IyztT5foMPx5vD0+c=}

  tailwindcss-animate@1.0.7:
    resolution: {integrity: sha1-MYtpLExCZ2zJ5nsZt4d1dCOIvvQ=}
    peerDependencies:
      tailwindcss: '>=3.0.0 || insiders'

  tailwindcss@4.1.7:
    resolution: {integrity: sha1-lJ921QZnlG3dcpHgx6S1p9/J52U=}

  tailwindcss@4.1.8:
    resolution: {integrity: sha1-XWbQle59gvA9bbxhWLwkjgZKXAU=}

  tapable@2.2.2:
    resolution: {integrity: sha1-q0mENA0wy5mJpJADLwhtu4tW2HI=}
    engines: {node: '>=6'}

  tar@7.4.3:
    resolution: {integrity: sha1-iLvpKGo/zZAOlFks2noisZLoBXE=}
    engines: {node: '>=18'}

  text-extensions@2.4.0:
    resolution: {integrity: sha1-oc/MUM802kG/0EfMdE+ATRaA6jQ=}
    engines: {node: '>=8'}

  through@2.3.8:
    resolution: {integrity: sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=}

  tinybench@2.9.0:
    resolution: {integrity: sha1-EDyfi6bXI3pHq23R3P93JRhjQms=}

  tinyexec@0.3.2:
    resolution: {integrity: sha1-lBeU5leoXklld5lcbu9m9T9Cs9I=}

  tinyexec@1.0.1:
    resolution: {integrity: sha1-cMMat6u7SuoKJPVdEg5ZkL+h4LE=}

  tinyglobby@0.2.13:
    resolution: {integrity: sha1-oORlFc5svNZTMVN+V0hK9aey/34=}
    engines: {node: '>=12.0.0'}

  tinypool@1.0.2:
    resolution: {integrity: sha1-cGGTzFMvTBAPZqoAsBxCFz2QUbI=}
    engines: {node: ^18.0.0 || >=20.0.0}

  tinyqueue@3.0.0:
    resolution: {integrity: sha1-EB6nYczIH5eeKSAJKeePFVbjZh4=}

  tinyrainbow@2.0.0:
    resolution: {integrity: sha1-lQmyFiQ2MV6A4+7g/M5EdNJEQpQ=}
    engines: {node: '>=14.0.0'}

  tinyspy@3.0.2:
    resolution: {integrity: sha1-ht0889c3sVrc8X14h8hKdSAd8go=}
    engines: {node: '>=14.0.0'}

  tldts-core@6.1.86:
    resolution: {integrity: sha1-qT5u2dUFy1TFQs5D/rFMc5EyZdg=}

  tldts@6.1.86:
    resolution: {integrity: sha1-CH4FVbMblyXuSMp+d+3FYRXNgvc=}
    hasBin: true

  to-regex-range@5.0.1:
    resolution: {integrity: sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=}
    engines: {node: '>=8.0'}

  tough-cookie@5.1.2:
    resolution: {integrity: sha1-Ztd0tKHZ4S3HUIlyWvOsdewxvtc=}
    engines: {node: '>=16'}

  tr46@5.1.1:
    resolution: {integrity: sha1-lq6GfN24/bZKScwwWajUKLzyOMo=}
    engines: {node: '>=18'}

  trie-memoize@1.2.0:
    resolution: {integrity: sha1-Oi6pMYcOteUaVHtCGlTVDKynizo=}

  ts-api-utils@2.1.0:
    resolution: {integrity: sha1-WV9wlORu7TZME/0j51+VE9Kbr5E=}
    engines: {node: '>=18.12'}
    peerDependencies:
      typescript: '>=4.8.4'

  tslib@2.8.1:
    resolution: {integrity: sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=}

  tsparticles@3.8.1:
    resolution: {integrity: sha1-SBAbCtW5166DbB949l+etQ2l0ho=}

  type-check@0.4.0:
    resolution: {integrity: sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=}
    engines: {node: '>= 0.8.0'}

  typed-array-buffer@1.0.3:
    resolution: {integrity: sha1-pyOVRQpIaewDP9VJNxtHrzou5TY=}
    engines: {node: '>= 0.4'}

  typed-array-byte-length@1.0.3:
    resolution: {integrity: sha1-hAegT314aE89JSqhoUPSt3tBYM4=}
    engines: {node: '>= 0.4'}

  typed-array-byte-offset@1.0.4:
    resolution: {integrity: sha1-rjaYuOyRqKuUUBYQiu8A1b/xI1U=}
    engines: {node: '>= 0.4'}

  typed-array-length@1.0.7:
    resolution: {integrity: sha1-7k3v+YS2S+HhGLDejJyHfVznPT0=}
    engines: {node: '>= 0.4'}

  typescript-eslint@8.32.1:
    resolution: {integrity: sha1-F4QzXHgUkb5Sj/hKtmbi8PdZH9E=}
    engines: {node: ^18.18.0 || ^20.9.0 || >=21.1.0}
    peerDependencies:
      eslint: ^8.57.0 || ^9.0.0
      typescript: '>=4.8.4 <5.9.0'

  typescript@5.8.3:
    resolution: {integrity: sha1-kvij5ePPSXNW9BeMNM1lp/XoRA4=}
    engines: {node: '>=14.17'}
    hasBin: true

  ua-parser-js@1.0.40:
    resolution: {integrity: sha1-rGr/T9jqPnlKaqdD7Jwvwp51tnU=}
    hasBin: true

  unbox-primitive@1.1.0:
    resolution: {integrity: sha1-jZ0snt7qhGDH81AzqIhnlEk00eI=}
    engines: {node: '>= 0.4'}

  undici-types@6.21.0:
    resolution: {integrity: sha1-aR0ArzkJvpOn+qE75hs6W1DvEss=}

  unicorn-magic@0.1.0:
    resolution: {integrity: sha1-G7mlHII6r51zqL/NPRoj3elLDOQ=}
    engines: {node: '>=18'}

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=}

  use-callback-ref@1.3.3:
    resolution: {integrity: sha1-mNn6sGcHWEHFssaFIJDV0P6r4r8=}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-sidecar@1.1.3:
    resolution: {integrity: sha1-EOf9iX0TC4luLFRsY6XoIz0A79s=}
    engines: {node: '>=10'}
    peerDependencies:
      '@types/react': '*'
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc
    peerDependenciesMeta:
      '@types/react':
        optional: true

  use-sync-external-store@1.5.0:
    resolution: {integrity: sha1-VRIuKj7dKmwQYXTCdIXg/Vm8/KA=}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  vite-node@3.1.4:
    resolution: {integrity: sha1-E/ELLLFVGXqXHLJ2FmTslSxsrhg=}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true

  vite@6.3.5:
    resolution: {integrity: sha1-/sc4eQE8nAEoyNKEUExtGUENEqM=}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': ^18.0.0 || ^20.0.0 || >=22.0.0
      jiti: '>=1.21.0'
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      sass-embedded: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.16.0
      tsx: ^4.8.1
      yaml: ^2.4.2
    peerDependenciesMeta:
      '@types/node':
        optional: true
      jiti:
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true
      tsx:
        optional: true
      yaml:
        optional: true

  vitest@3.1.4:
    resolution: {integrity: sha1-X0lbfbsdTSCLiFCM1N/OsAb4t+Y=}
    engines: {node: ^18.0.0 || ^20.0.0 || >=22.0.0}
    hasBin: true
    peerDependencies:
      '@edge-runtime/vm': '*'
      '@types/debug': ^4.1.12
      '@types/node': ^18.0.0 || ^20.0.0 || >=22.0.0
      '@vitest/browser': 3.1.4
      '@vitest/ui': 3.1.4
      happy-dom: '*'
      jsdom: '*'
    peerDependenciesMeta:
      '@edge-runtime/vm':
        optional: true
      '@types/debug':
        optional: true
      '@types/node':
        optional: true
      '@vitest/browser':
        optional: true
      '@vitest/ui':
        optional: true
      happy-dom:
        optional: true
      jsdom:
        optional: true

  w3c-xmlserializer@5.0.0:
    resolution: {integrity: sha1-+SW6JoVRWFlNkHMTzt0UdsWWf2w=}
    engines: {node: '>=18'}

  web-vitals@4.2.4:
    resolution: {integrity: sha1-HSC8hZCjd2m9CQKyiVUJNgaRhLc=}

  web-worker@1.5.0:
    resolution: {integrity: sha1-cbKw+8xCk+jwqk9rij/+v/cz3MU=}

  webidl-conversions@7.0.0:
    resolution: {integrity: sha1-JWtOGIK+feu/AdBfCqIDl3jqCAo=}
    engines: {node: '>=12'}

  whatwg-encoding@3.1.1:
    resolution: {integrity: sha1-0PTvdpkF1CbhaI8+NDgambYLduU=}
    engines: {node: '>=18'}

  whatwg-mimetype@4.0.0:
    resolution: {integrity: sha1-vBv5SphdxQOI1UqSWKxAXDyi/Ao=}
    engines: {node: '>=18'}

  whatwg-url@14.2.0:
    resolution: {integrity: sha1-TuAtXXJRVdrgBPaulcc+fvXZVmM=}
    engines: {node: '>=18'}

  which-boxed-primitive@1.1.1:
    resolution: {integrity: sha1-127Cfff6Fl8Y1YCDdKX+I8KbF24=}
    engines: {node: '>= 0.4'}

  which-builtin-type@1.2.1:
    resolution: {integrity: sha1-iRg9obSQerCJprAgKcxdjWV0Jw4=}
    engines: {node: '>= 0.4'}

  which-collection@1.0.2:
    resolution: {integrity: sha1-Yn73YkOSChB+fOjpYZHevksWwqA=}
    engines: {node: '>= 0.4'}

  which-typed-array@1.1.19:
    resolution: {integrity: sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=}
    engines: {node: '>= 0.4'}

  which@2.0.2:
    resolution: {integrity: sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=}
    engines: {node: '>= 8'}
    hasBin: true

  why-is-node-running@2.3.0:
    resolution: {integrity: sha1-o/aalxB/SUs83Dvd3Yg6fWXOvwQ=}
    engines: {node: '>=8'}
    hasBin: true

  wildcard@1.1.2:
    resolution: {integrity: sha1-pwIEUwhNjNLv5wup02liY94XEKU=}

  word-wrap@1.2.5:
    resolution: {integrity: sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=}
    engines: {node: '>=0.10.0'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=}
    engines: {node: '>=10'}

  wrap-ansi@9.0.0:
    resolution: {integrity: sha1-Gj3Itw2F7rg5jd+x5KAs0Ybliz4=}
    engines: {node: '>=18'}

  ws@8.18.2:
    resolution: {integrity: sha1-QnOLK+V87YX0YVQyCqu1GrADcFo=}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xml-name-validator@5.0.0:
    resolution: {integrity: sha1-gr6blX96/az5YeWYDxvyJ8C/dnM=}
    engines: {node: '>=18'}

  xml-utils@1.10.2:
    resolution: {integrity: sha1-Q2s5zMJaZjzjZ+ohq7cXr96l1rE=}

  xmlchars@2.2.0:
    resolution: {integrity: sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=}

  y18n@5.0.8:
    resolution: {integrity: sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=}
    engines: {node: '>=10'}

  yallist@3.1.1:
    resolution: {integrity: sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=}

  yallist@5.0.0:
    resolution: {integrity: sha1-AOLeRDY57Q14/YfeDSdGn7z/tTM=}
    engines: {node: '>=18'}

  yaml@1.10.2:
    resolution: {integrity: sha1-IwHF/78StGfejaIzOkWeKeeSDks=}
    engines: {node: '>= 6'}

  yaml@2.8.0:
    resolution: {integrity: sha1-FfjJhmIRvcLTeBoIkORNT6Gl//Y=}
    engines: {node: '>= 14.6'}
    hasBin: true

  yargs-parser@21.1.1:
    resolution: {integrity: sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=}
    engines: {node: '>=12'}

  yargs@17.7.2:
    resolution: {integrity: sha1-mR3zmspnWhkrgW4eA2P5110qomk=}
    engines: {node: '>=12'}

  yocto-queue@0.1.0:
    resolution: {integrity: sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=}
    engines: {node: '>=10'}

  yocto-queue@1.2.1:
    resolution: {integrity: sha1-NtfEc593Wzy8KOYTbiGqBXrexBg=}
    engines: {node: '>=12.20'}

  zstddec@0.1.0:
    resolution: {integrity: sha1-cFDz8ODDl4Vi0MVms+WkJ9K61+w=}

  zustand@5.0.5:
    resolution: {integrity: sha1-PiNvapUxQtl1M20Xm8c12X2xfoQ=}
    engines: {node: '>=12.20.0'}
    peerDependencies:
      '@types/react': '>=18.0.0'
      immer: '>=9.0.6'
      react: '>=18.0.0'
      use-sync-external-store: '>=1.2.0'
    peerDependenciesMeta:
      '@types/react':
        optional: true
      immer:
        optional: true
      react:
        optional: true
      use-sync-external-store:
        optional: true

snapshots:

  '@adobe/css-tools@4.4.3': {}

  '@alloc/quick-lru@5.2.0': {}

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@asamuzakjp/css-color@3.2.0':
    dependencies:
      '@csstools/css-calc': 2.1.3(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-color-parser': 3.0.9(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-parser-algorithms': 3.0.4(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-tokenizer': 3.0.3
      lru-cache: 10.4.3

  '@babel/code-frame@7.27.1':
    dependencies:
      '@babel/helper-validator-identifier': 7.27.1
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.27.2': {}

  '@babel/core@7.27.1':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.1
      '@babel/helper-compilation-targets': 7.27.2
      '@babel/helper-module-transforms': 7.27.1(@babel/core@7.27.1)
      '@babel/helpers': 7.27.1
      '@babel/parser': 7.27.2
      '@babel/template': 7.27.2
      '@babel/traverse': 7.27.1
      '@babel/types': 7.27.1
      convert-source-map: 2.0.0
      debug: 4.4.1
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.27.1':
    dependencies:
      '@babel/parser': 7.27.2
      '@babel/types': 7.27.1
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.1.0

  '@babel/helper-compilation-targets@7.27.2':
    dependencies:
      '@babel/compat-data': 7.27.2
      '@babel/helper-validator-option': 7.27.1
      browserslist: 4.24.5
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-module-imports@7.27.1':
    dependencies:
      '@babel/traverse': 7.27.1
      '@babel/types': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.27.1(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-module-imports': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1
      '@babel/traverse': 7.27.1
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-plugin-utils@7.27.1': {}

  '@babel/helper-string-parser@7.27.1': {}

  '@babel/helper-validator-identifier@7.27.1': {}

  '@babel/helper-validator-option@7.27.1': {}

  '@babel/helpers@7.27.1':
    dependencies:
      '@babel/template': 7.27.2
      '@babel/types': 7.27.1

  '@babel/parser@7.27.2':
    dependencies:
      '@babel/types': 7.27.1

  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.27.1)':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/helper-plugin-utils': 7.27.1

  '@babel/runtime@7.27.1': {}

  '@babel/template@7.27.2':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/parser': 7.27.2
      '@babel/types': 7.27.1

  '@babel/traverse@7.27.1':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/generator': 7.27.1
      '@babel/parser': 7.27.2
      '@babel/template': 7.27.2
      '@babel/types': 7.27.1
      debug: 4.4.1
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.27.1':
    dependencies:
      '@babel/helper-string-parser': 7.27.1
      '@babel/helper-validator-identifier': 7.27.1

  '@commitlint/cli@19.8.1(@types/node@22.15.21)(typescript@5.8.3)':
    dependencies:
      '@commitlint/format': 19.8.1
      '@commitlint/lint': 19.8.1
      '@commitlint/load': 19.8.1(@types/node@22.15.21)(typescript@5.8.3)
      '@commitlint/read': 19.8.1
      '@commitlint/types': 19.8.1
      tinyexec: 1.0.1
      yargs: 17.7.2
    transitivePeerDependencies:
      - '@types/node'
      - typescript

  '@commitlint/config-conventional@19.8.1':
    dependencies:
      '@commitlint/types': 19.8.1
      conventional-changelog-conventionalcommits: 7.0.2

  '@commitlint/config-validator@19.8.1':
    dependencies:
      '@commitlint/types': 19.8.1
      ajv: 8.17.1

  '@commitlint/ensure@19.8.1':
    dependencies:
      '@commitlint/types': 19.8.1
      lodash.camelcase: 4.3.0
      lodash.kebabcase: 4.1.1
      lodash.snakecase: 4.1.1
      lodash.startcase: 4.4.0
      lodash.upperfirst: 4.3.1

  '@commitlint/execute-rule@19.8.1': {}

  '@commitlint/format@19.8.1':
    dependencies:
      '@commitlint/types': 19.8.1
      chalk: 5.4.1

  '@commitlint/is-ignored@19.8.1':
    dependencies:
      '@commitlint/types': 19.8.1
      semver: 7.7.2

  '@commitlint/lint@19.8.1':
    dependencies:
      '@commitlint/is-ignored': 19.8.1
      '@commitlint/parse': 19.8.1
      '@commitlint/rules': 19.8.1
      '@commitlint/types': 19.8.1

  '@commitlint/load@19.8.1(@types/node@22.15.21)(typescript@5.8.3)':
    dependencies:
      '@commitlint/config-validator': 19.8.1
      '@commitlint/execute-rule': 19.8.1
      '@commitlint/resolve-extends': 19.8.1
      '@commitlint/types': 19.8.1
      chalk: 5.4.1
      cosmiconfig: 9.0.0(typescript@5.8.3)
      cosmiconfig-typescript-loader: 6.1.0(@types/node@22.15.21)(cosmiconfig@9.0.0(typescript@5.8.3))(typescript@5.8.3)
      lodash.isplainobject: 4.0.6
      lodash.merge: 4.6.2
      lodash.uniq: 4.5.0
    transitivePeerDependencies:
      - '@types/node'
      - typescript

  '@commitlint/message@19.8.1': {}

  '@commitlint/parse@19.8.1':
    dependencies:
      '@commitlint/types': 19.8.1
      conventional-changelog-angular: 7.0.0
      conventional-commits-parser: 5.0.0

  '@commitlint/read@19.8.1':
    dependencies:
      '@commitlint/top-level': 19.8.1
      '@commitlint/types': 19.8.1
      git-raw-commits: 4.0.0
      minimist: 1.2.8
      tinyexec: 1.0.1

  '@commitlint/resolve-extends@19.8.1':
    dependencies:
      '@commitlint/config-validator': 19.8.1
      '@commitlint/types': 19.8.1
      global-directory: 4.0.1
      import-meta-resolve: 4.1.0
      lodash.mergewith: 4.6.2
      resolve-from: 5.0.0

  '@commitlint/rules@19.8.1':
    dependencies:
      '@commitlint/ensure': 19.8.1
      '@commitlint/message': 19.8.1
      '@commitlint/to-lines': 19.8.1
      '@commitlint/types': 19.8.1

  '@commitlint/to-lines@19.8.1': {}

  '@commitlint/top-level@19.8.1':
    dependencies:
      find-up: 7.0.0

  '@commitlint/types@19.8.1':
    dependencies:
      '@types/conventional-commits-parser': 5.0.1
      chalk: 5.4.1

  '@csstools/color-helpers@5.0.2': {}

  '@csstools/css-calc@2.1.3(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    dependencies:
      '@csstools/css-parser-algorithms': 3.0.4(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-tokenizer': 3.0.3

  '@csstools/css-color-parser@3.0.9(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)':
    dependencies:
      '@csstools/color-helpers': 5.0.2
      '@csstools/css-calc': 2.1.3(@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3))(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-parser-algorithms': 3.0.4(@csstools/css-tokenizer@3.0.3)
      '@csstools/css-tokenizer': 3.0.3

  '@csstools/css-parser-algorithms@3.0.4(@csstools/css-tokenizer@3.0.3)':
    dependencies:
      '@csstools/css-tokenizer': 3.0.3

  '@csstools/css-tokenizer@3.0.3': {}

  '@dvsl/zoomcharts@1.21.4': {}

  '@emotion/babel-plugin@11.13.5':
    dependencies:
      '@babel/helper-module-imports': 7.27.1
      '@babel/runtime': 7.27.1
      '@emotion/hash': 0.9.2
      '@emotion/memoize': 0.9.0
      '@emotion/serialize': 1.3.3
      babel-plugin-macros: 3.1.0
      convert-source-map: 1.9.0
      escape-string-regexp: 4.0.0
      find-root: 1.1.0
      source-map: 0.5.7
      stylis: 4.2.0
    transitivePeerDependencies:
      - supports-color

  '@emotion/cache@11.14.0':
    dependencies:
      '@emotion/memoize': 0.9.0
      '@emotion/sheet': 1.4.0
      '@emotion/utils': 1.4.2
      '@emotion/weak-memoize': 0.4.0
      stylis: 4.2.0

  '@emotion/hash@0.9.2': {}

  '@emotion/is-prop-valid@1.3.1':
    dependencies:
      '@emotion/memoize': 0.9.0

  '@emotion/memoize@0.9.0': {}

  '@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      '@babel/runtime': 7.27.1
      '@emotion/babel-plugin': 11.13.5
      '@emotion/cache': 11.14.0
      '@emotion/serialize': 1.3.3
      '@emotion/use-insertion-effect-with-fallbacks': 1.2.0(react@19.1.0)
      '@emotion/utils': 1.4.2
      '@emotion/weak-memoize': 0.4.0
      hoist-non-react-statics: 3.3.2
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5
    transitivePeerDependencies:
      - supports-color

  '@emotion/serialize@1.3.3':
    dependencies:
      '@emotion/hash': 0.9.2
      '@emotion/memoize': 0.9.0
      '@emotion/unitless': 0.10.0
      '@emotion/utils': 1.4.2
      csstype: 3.1.3

  '@emotion/sheet@1.4.0': {}

  '@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      '@babel/runtime': 7.27.1
      '@emotion/babel-plugin': 11.13.5
      '@emotion/is-prop-valid': 1.3.1
      '@emotion/react': 11.14.0(@types/react@19.1.5)(react@19.1.0)
      '@emotion/serialize': 1.3.3
      '@emotion/use-insertion-effect-with-fallbacks': 1.2.0(react@19.1.0)
      '@emotion/utils': 1.4.2
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5
    transitivePeerDependencies:
      - supports-color

  '@emotion/unitless@0.10.0': {}

  '@emotion/use-insertion-effect-with-fallbacks@1.2.0(react@19.1.0)':
    dependencies:
      react: 19.1.0

  '@emotion/utils@1.4.2': {}

  '@emotion/weak-memoize@0.4.0': {}

  '@esbuild/aix-ppc64@0.25.4':
    optional: true

  '@esbuild/android-arm64@0.25.4':
    optional: true

  '@esbuild/android-arm@0.25.4':
    optional: true

  '@esbuild/android-x64@0.25.4':
    optional: true

  '@esbuild/darwin-arm64@0.25.4':
    optional: true

  '@esbuild/darwin-x64@0.25.4':
    optional: true

  '@esbuild/freebsd-arm64@0.25.4':
    optional: true

  '@esbuild/freebsd-x64@0.25.4':
    optional: true

  '@esbuild/linux-arm64@0.25.4':
    optional: true

  '@esbuild/linux-arm@0.25.4':
    optional: true

  '@esbuild/linux-ia32@0.25.4':
    optional: true

  '@esbuild/linux-loong64@0.25.4':
    optional: true

  '@esbuild/linux-mips64el@0.25.4':
    optional: true

  '@esbuild/linux-ppc64@0.25.4':
    optional: true

  '@esbuild/linux-riscv64@0.25.4':
    optional: true

  '@esbuild/linux-s390x@0.25.4':
    optional: true

  '@esbuild/linux-x64@0.25.4':
    optional: true

  '@esbuild/netbsd-arm64@0.25.4':
    optional: true

  '@esbuild/netbsd-x64@0.25.4':
    optional: true

  '@esbuild/openbsd-arm64@0.25.4':
    optional: true

  '@esbuild/openbsd-x64@0.25.4':
    optional: true

  '@esbuild/sunos-x64@0.25.4':
    optional: true

  '@esbuild/win32-arm64@0.25.4':
    optional: true

  '@esbuild/win32-ia32@0.25.4':
    optional: true

  '@esbuild/win32-x64@0.25.4':
    optional: true

  '@eslint-community/eslint-utils@4.7.0(eslint@9.27.0(jiti@2.4.2))':
    dependencies:
      eslint: 9.27.0(jiti@2.4.2)
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/config-array@0.20.0':
    dependencies:
      '@eslint/object-schema': 2.1.6
      debug: 4.4.1
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@eslint/config-helpers@0.2.2': {}

  '@eslint/core@0.14.0':
    dependencies:
      '@types/json-schema': 7.0.15

  '@eslint/eslintrc@3.3.1':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.1
      espree: 10.3.0
      globals: 14.0.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@eslint/js@9.27.0': {}

  '@eslint/object-schema@2.1.6': {}

  '@eslint/plugin-kit@0.3.1':
    dependencies:
      '@eslint/core': 0.14.0
      levn: 0.4.1

  '@essentials/memoize-one@1.1.0': {}

  '@essentials/one-key-map@1.2.0': {}

  '@essentials/raf@1.2.0': {}

  '@essentials/request-timeout@1.3.0':
    dependencies:
      '@essentials/raf': 1.2.0

  '@floating-ui/core@1.7.0':
    dependencies:
      '@floating-ui/utils': 0.2.9

  '@floating-ui/dom@1.7.0':
    dependencies:
      '@floating-ui/core': 1.7.0
      '@floating-ui/utils': 0.2.9

  '@floating-ui/react-dom@2.1.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@floating-ui/dom': 1.7.0
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@floating-ui/utils@0.2.9': {}

  '@grafana/faro-core@1.18.2':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/otlp-transformer': 0.201.1(@opentelemetry/api@1.9.0)

  '@grafana/faro-web-sdk@1.18.2':
    dependencies:
      '@grafana/faro-core': 1.18.2
      ua-parser-js: 1.0.40
      web-vitals: 4.2.4

  '@grafana/faro-web-tracing@1.18.2':
    dependencies:
      '@grafana/faro-web-sdk': 1.18.2
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/exporter-trace-otlp-http': 0.201.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.201.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-fetch': 0.201.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation-xml-http-request': 0.201.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/otlp-transformer': 0.201.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/resources': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-web': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.33.1
    transitivePeerDependencies:
      - supports-color

  '@humanfs/core@0.19.1': {}

  '@humanfs/node@0.16.6':
    dependencies:
      '@humanfs/core': 0.19.1
      '@humanwhocodes/retry': 0.3.1

  '@humanwhocodes/module-importer@1.0.1': {}

  '@humanwhocodes/retry@0.3.1': {}

  '@humanwhocodes/retry@0.4.3': {}

  '@isaacs/fs-minipass@4.0.1':
    dependencies:
      minipass: 7.1.2

  '@jridgewell/gen-mapping@0.3.8':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@mapbox/jsonlint-lines-primitives@2.0.2': {}

  '@mapbox/unitbezier@0.0.1': {}

  '@maplibre/maplibre-gl-style-spec@23.3.0':
    dependencies:
      '@mapbox/jsonlint-lines-primitives': 2.0.2
      '@mapbox/unitbezier': 0.0.1
      json-stringify-pretty-compact: 4.0.0
      minimist: 1.2.8
      quickselect: 3.0.0
      rw: 1.3.3
      tinyqueue: 3.0.0

  '@mui/core-downloads-tracker@7.1.0': {}

  '@mui/icons-material@7.1.0(@mui/material@7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      '@babel/runtime': 7.27.1
      '@mui/material': 7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  '@mui/lab@7.0.0-beta.12(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@mui/material@7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@babel/runtime': 7.27.1
      '@mui/material': 7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@mui/system': 7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0)
      '@mui/types': 7.4.2(@types/react@19.1.5)
      '@mui/utils': 7.1.0(@types/react@19.1.5)(react@19.1.0)
      clsx: 2.1.1
      prop-types: 15.8.1
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@emotion/react': 11.14.0(@types/react@19.1.5)(react@19.1.0)
      '@emotion/styled': 11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0)
      '@types/react': 19.1.5

  '@mui/material@7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@babel/runtime': 7.27.1
      '@mui/core-downloads-tracker': 7.1.0
      '@mui/system': 7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0)
      '@mui/types': 7.4.2(@types/react@19.1.5)
      '@mui/utils': 7.1.0(@types/react@19.1.5)(react@19.1.0)
      '@popperjs/core': 2.11.8
      '@types/react-transition-group': 4.4.12(@types/react@19.1.5)
      clsx: 2.1.1
      csstype: 3.1.3
      prop-types: 15.8.1
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-is: 19.1.0
      react-transition-group: 4.4.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
    optionalDependencies:
      '@emotion/react': 11.14.0(@types/react@19.1.5)(react@19.1.0)
      '@emotion/styled': 11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0)
      '@types/react': 19.1.5

  '@mui/private-theming@7.1.0(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      '@babel/runtime': 7.27.1
      '@mui/utils': 7.1.0(@types/react@19.1.5)(react@19.1.0)
      prop-types: 15.8.1
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  '@mui/styled-engine@7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@babel/runtime': 7.27.1
      '@emotion/cache': 11.14.0
      '@emotion/serialize': 1.3.3
      '@emotion/sheet': 1.4.0
      csstype: 3.1.3
      prop-types: 15.8.1
      react: 19.1.0
    optionalDependencies:
      '@emotion/react': 11.14.0(@types/react@19.1.5)(react@19.1.0)
      '@emotion/styled': 11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0)

  '@mui/system@7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      '@babel/runtime': 7.27.1
      '@mui/private-theming': 7.1.0(@types/react@19.1.5)(react@19.1.0)
      '@mui/styled-engine': 7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(react@19.1.0)
      '@mui/types': 7.4.2(@types/react@19.1.5)
      '@mui/utils': 7.1.0(@types/react@19.1.5)(react@19.1.0)
      clsx: 2.1.1
      csstype: 3.1.3
      prop-types: 15.8.1
      react: 19.1.0
    optionalDependencies:
      '@emotion/react': 11.14.0(@types/react@19.1.5)(react@19.1.0)
      '@emotion/styled': 11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0)
      '@types/react': 19.1.5

  '@mui/types@7.4.2(@types/react@19.1.5)':
    dependencies:
      '@babel/runtime': 7.27.1
    optionalDependencies:
      '@types/react': 19.1.5

  '@mui/utils@7.1.0(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      '@babel/runtime': 7.27.1
      '@mui/types': 7.4.2(@types/react@19.1.5)
      '@types/prop-types': 15.7.14
      clsx: 2.1.1
      prop-types: 15.8.1
      react: 19.1.0
      react-is: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  '@mui/x-date-pickers@8.4.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@mui/material@7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0))(@mui/system@7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(date-fns@4.1.0)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@babel/runtime': 7.27.1
      '@mui/material': 7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@mui/system': 7.1.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@emotion/styled@11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0)
      '@mui/utils': 7.1.0(@types/react@19.1.5)(react@19.1.0)
      '@mui/x-internals': 8.4.0(@types/react@19.1.5)(react@19.1.0)
      '@types/react-transition-group': 4.4.12(@types/react@19.1.5)
      clsx: 2.1.1
      prop-types: 15.8.1
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-transition-group: 4.4.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
    optionalDependencies:
      '@emotion/react': 11.14.0(@types/react@19.1.5)(react@19.1.0)
      '@emotion/styled': 11.14.0(@emotion/react@11.14.0(@types/react@19.1.5)(react@19.1.0))(@types/react@19.1.5)(react@19.1.0)
      date-fns: 4.1.0
    transitivePeerDependencies:
      - '@types/react'

  '@mui/x-internals@8.4.0(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      '@babel/runtime': 7.27.1
      '@mui/utils': 7.1.0(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
    transitivePeerDependencies:
      - '@types/react'

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1

  '@opentelemetry/api-logs@0.201.1':
    dependencies:
      '@opentelemetry/api': 1.9.0

  '@opentelemetry/api@1.9.0': {}

  '@opentelemetry/core@2.0.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/semantic-conventions': 1.33.1

  '@opentelemetry/exporter-trace-otlp-http@0.201.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/otlp-exporter-base': 0.201.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/otlp-transformer': 0.201.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/resources': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-base': 2.0.1(@opentelemetry/api@1.9.0)

  '@opentelemetry/instrumentation-fetch@0.201.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.201.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-web': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.33.1
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation-xml-http-request@0.201.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/instrumentation': 0.201.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-web': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.33.1
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/instrumentation@0.201.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/api-logs': 0.201.1
      '@types/shimmer': 1.2.0
      import-in-the-middle: 1.13.2
      require-in-the-middle: 7.5.2
      shimmer: 1.2.1
    transitivePeerDependencies:
      - supports-color

  '@opentelemetry/otlp-exporter-base@0.201.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/otlp-transformer': 0.201.1(@opentelemetry/api@1.9.0)

  '@opentelemetry/otlp-transformer@0.201.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/api-logs': 0.201.1
      '@opentelemetry/core': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/resources': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-logs': 0.201.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-metrics': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-base': 2.0.1(@opentelemetry/api@1.9.0)
      protobufjs: 7.4.0

  '@opentelemetry/resources@2.0.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.33.1

  '@opentelemetry/sdk-logs@0.201.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/api-logs': 0.201.1
      '@opentelemetry/core': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/resources': 2.0.1(@opentelemetry/api@1.9.0)

  '@opentelemetry/sdk-metrics@2.0.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/resources': 2.0.1(@opentelemetry/api@1.9.0)

  '@opentelemetry/sdk-trace-base@2.0.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/resources': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/semantic-conventions': 1.33.1

  '@opentelemetry/sdk-trace-web@2.0.1(@opentelemetry/api@1.9.0)':
    dependencies:
      '@opentelemetry/api': 1.9.0
      '@opentelemetry/core': 2.0.1(@opentelemetry/api@1.9.0)
      '@opentelemetry/sdk-trace-base': 2.0.1(@opentelemetry/api@1.9.0)

  '@opentelemetry/semantic-conventions@1.33.1': {}

  '@petamoriken/float16@3.9.2': {}

  '@pkgr/core@0.2.4': {}

  '@playwright/test@1.52.0':
    dependencies:
      playwright: 1.52.0

  '@popperjs/core@2.11.8': {}

  '@protobufjs/aspromise@1.1.2': {}

  '@protobufjs/base64@1.1.2': {}

  '@protobufjs/codegen@2.0.4': {}

  '@protobufjs/eventemitter@1.1.0': {}

  '@protobufjs/fetch@1.1.0':
    dependencies:
      '@protobufjs/aspromise': 1.1.2
      '@protobufjs/inquire': 1.1.0

  '@protobufjs/float@1.0.2': {}

  '@protobufjs/inquire@1.1.0': {}

  '@protobufjs/path@1.1.2': {}

  '@protobufjs/pool@1.1.0': {}

  '@protobufjs/utf8@1.1.0': {}

  '@radix-ui/number@1.1.1': {}

  '@radix-ui/primitive@1.1.2': {}

  '@radix-ui/react-accordion@1.2.11(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collapsible': 1.1.11(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-collection': 1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-arrow@1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-avatar@1.1.10(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-is-hydrated': 0.1.0(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-checkbox@1.3.2(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-presence': 1.1.4(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-previous': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-size': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-collapsible@1.1.11(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-presence': 1.1.4(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-collection@1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-compose-refs@1.1.2(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  '@radix-ui/react-context@1.1.2(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  '@radix-ui/react-dialog@1.1.14(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-dismissable-layer': 1.1.10(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-focus-guards': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-focus-scope': 1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-portal': 1.1.9(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-presence': 1.1.4(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.5)(react@19.1.0)
      aria-hidden: 1.2.6
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-remove-scroll: 2.7.0(@types/react@19.1.5)(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-direction@1.1.1(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  '@radix-ui/react-dismissable-layer@1.1.10(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-escape-keydown': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-dropdown-menu@2.1.15(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-menu': 2.1.15(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-focus-guards@1.1.2(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  '@radix-ui/react-focus-scope@1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-id@1.1.1(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  '@radix-ui/react-label@2.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-menu@2.1.15(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-dismissable-layer': 1.1.10(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-focus-guards': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-focus-scope': 1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-popper': 1.2.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-portal': 1.1.9(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-presence': 1.1.4(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-roving-focus': 1.1.10(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      aria-hidden: 1.2.6
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-remove-scroll: 2.7.0(@types/react@19.1.5)(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-popover@1.1.14(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-dismissable-layer': 1.1.10(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-focus-guards': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-focus-scope': 1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-popper': 1.2.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-portal': 1.1.9(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-presence': 1.1.4(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.5)(react@19.1.0)
      aria-hidden: 1.2.6
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-remove-scroll: 2.7.0(@types/react@19.1.5)(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-popper@1.2.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@floating-ui/react-dom': 2.1.2(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-arrow': 1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-rect': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-size': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/rect': 1.1.1
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-portal@1.1.9(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-presence@1.1.4(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-primitive@2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-roving-focus@1.1.10(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-select@2.2.5(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/number': 1.1.1
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-dismissable-layer': 1.1.10(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-focus-guards': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-focus-scope': 1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-popper': 1.2.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-portal': 1.1.9(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-previous': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-visually-hidden': 1.2.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      aria-hidden: 1.2.6
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-remove-scroll: 2.7.0(@types/react@19.1.5)(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-separator@1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-slider@1.3.5(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/number': 1.1.1
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-collection': 1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-previous': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-size': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-slot@1.2.3(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  '@radix-ui/react-tabs@1.1.12(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-direction': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-presence': 1.1.4(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-roving-focus': 1.1.10(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-tooltip@1.2.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/primitive': 1.1.2
      '@radix-ui/react-compose-refs': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-context': 1.1.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-dismissable-layer': 1.1.10(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-id': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-popper': 1.2.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-portal': 1.1.9(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-presence': 1.1.4(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-controllable-state': 1.2.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-visually-hidden': 1.2.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/react-use-callback-ref@1.1.1(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  '@radix-ui/react-use-controllable-state@1.2.2(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-use-effect-event': 0.0.2(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  '@radix-ui/react-use-effect-event@0.0.2(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  '@radix-ui/react-use-escape-keydown@1.1.1(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-use-callback-ref': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  '@radix-ui/react-use-is-hydrated@0.1.0(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      react: 19.1.0
      use-sync-external-store: 1.5.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5

  '@radix-ui/react-use-layout-effect@1.1.1(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  '@radix-ui/react-use-previous@1.1.1(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  '@radix-ui/react-use-rect@1.1.1(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      '@radix-ui/rect': 1.1.1
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  '@radix-ui/react-use-size@1.1.1(@types/react@19.1.5)(react@19.1.0)':
    dependencies:
      '@radix-ui/react-use-layout-effect': 1.1.1(@types/react@19.1.5)(react@19.1.0)
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  '@radix-ui/react-visually-hidden@1.2.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@radix-ui/react-primitive': 2.1.3(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@radix-ui/rect@1.1.1': {}

  '@react-hook/debounce@3.0.0(react@19.1.0)':
    dependencies:
      '@react-hook/latest': 1.0.3(react@19.1.0)
      react: 19.1.0

  '@react-hook/event@1.2.6(react@19.1.0)':
    dependencies:
      react: 19.1.0

  '@react-hook/latest@1.0.3(react@19.1.0)':
    dependencies:
      react: 19.1.0

  '@react-hook/passive-layout-effect@1.2.1(react@19.1.0)':
    dependencies:
      react: 19.1.0

  '@react-hook/throttle@2.2.0(react@19.1.0)':
    dependencies:
      '@react-hook/latest': 1.0.3(react@19.1.0)
      react: 19.1.0

  '@react-hook/window-scroll@1.3.0(react@19.1.0)':
    dependencies:
      '@react-hook/event': 1.2.6(react@19.1.0)
      '@react-hook/throttle': 2.2.0(react@19.1.0)
      react: 19.1.0

  '@react-hook/window-size@3.1.1(react@19.1.0)':
    dependencies:
      '@react-hook/debounce': 3.0.0(react@19.1.0)
      '@react-hook/event': 1.2.6(react@19.1.0)
      '@react-hook/throttle': 2.2.0(react@19.1.0)
      react: 19.1.0

  '@remix-run/router@1.23.0': {}

  '@rollup/rollup-android-arm-eabi@4.41.0':
    optional: true

  '@rollup/rollup-android-arm64@4.41.0':
    optional: true

  '@rollup/rollup-darwin-arm64@4.41.0':
    optional: true

  '@rollup/rollup-darwin-x64@4.41.0':
    optional: true

  '@rollup/rollup-freebsd-arm64@4.41.0':
    optional: true

  '@rollup/rollup-freebsd-x64@4.41.0':
    optional: true

  '@rollup/rollup-linux-arm-gnueabihf@4.41.0':
    optional: true

  '@rollup/rollup-linux-arm-musleabihf@4.41.0':
    optional: true

  '@rollup/rollup-linux-arm64-gnu@4.41.0':
    optional: true

  '@rollup/rollup-linux-arm64-musl@4.41.0':
    optional: true

  '@rollup/rollup-linux-loongarch64-gnu@4.41.0':
    optional: true

  '@rollup/rollup-linux-powerpc64le-gnu@4.41.0':
    optional: true

  '@rollup/rollup-linux-riscv64-gnu@4.41.0':
    optional: true

  '@rollup/rollup-linux-riscv64-musl@4.41.0':
    optional: true

  '@rollup/rollup-linux-s390x-gnu@4.41.0':
    optional: true

  '@rollup/rollup-linux-x64-gnu@4.41.0':
    optional: true

  '@rollup/rollup-linux-x64-musl@4.41.0':
    optional: true

  '@rollup/rollup-win32-arm64-msvc@4.41.0':
    optional: true

  '@rollup/rollup-win32-ia32-msvc@4.41.0':
    optional: true

  '@rollup/rollup-win32-x64-msvc@4.41.0':
    optional: true

  '@snap/design-system@1.2.4(@emotion/is-prop-valid@1.3.1)(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(immer@10.1.1)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)(tailwindcss@4.1.7)(use-sync-external-store@1.5.0(react@19.1.0))':
    dependencies:
      '@radix-ui/react-accordion': 1.2.11(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-avatar': 1.1.10(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-checkbox': 1.3.2(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-collapsible': 1.1.11(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-dialog': 1.1.14(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-label': 2.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-select': 2.2.5(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-separator': 1.1.7(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slider': 1.3.5(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@radix-ui/react-slot': 1.2.3(@types/react@19.1.5)(react@19.1.0)
      '@radix-ui/react-tabs': 1.1.12(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      '@react-hook/window-size': 3.1.1(react@19.1.0)
      '@tailwindcss/postcss': 4.1.8
      class-variance-authority: 0.7.1
      clsx: 2.1.1
      framer-motion: 12.12.1(@emotion/is-prop-valid@1.3.1)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)
      lucide-react: 0.476.0(react@19.1.0)
      masonic: 4.1.0(react@19.1.0)
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-layout-masonry: 1.2.0(react@19.1.0)
      react-photo-album: 3.1.0(@types/react@19.1.5)(react@19.1.0)
      react-responsive-masonry: 2.7.1
      tailwind-merge: 3.3.0
      tailwindcss-animate: 1.0.7(tailwindcss@4.1.7)
      zustand: 5.0.5(@types/react@19.1.5)(immer@10.1.1)(react@19.1.0)(use-sync-external-store@1.5.0(react@19.1.0))
    transitivePeerDependencies:
      - '@emotion/is-prop-valid'
      - '@types/react'
      - '@types/react-dom'
      - immer
      - tailwindcss
      - use-sync-external-store

  '@tailwindcss/node@4.1.7':
    dependencies:
      '@ampproject/remapping': 2.3.0
      enhanced-resolve: 5.18.1
      jiti: 2.4.2
      lightningcss: 1.30.1
      magic-string: 0.30.17
      source-map-js: 1.2.1
      tailwindcss: 4.1.7

  '@tailwindcss/node@4.1.8':
    dependencies:
      '@ampproject/remapping': 2.3.0
      enhanced-resolve: 5.18.1
      jiti: 2.4.2
      lightningcss: 1.30.1
      magic-string: 0.30.17
      source-map-js: 1.2.1
      tailwindcss: 4.1.8

  '@tailwindcss/oxide-android-arm64@4.1.7':
    optional: true

  '@tailwindcss/oxide-android-arm64@4.1.8':
    optional: true

  '@tailwindcss/oxide-darwin-arm64@4.1.7':
    optional: true

  '@tailwindcss/oxide-darwin-arm64@4.1.8':
    optional: true

  '@tailwindcss/oxide-darwin-x64@4.1.7':
    optional: true

  '@tailwindcss/oxide-darwin-x64@4.1.8':
    optional: true

  '@tailwindcss/oxide-freebsd-x64@4.1.7':
    optional: true

  '@tailwindcss/oxide-freebsd-x64@4.1.8':
    optional: true

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.7':
    optional: true

  '@tailwindcss/oxide-linux-arm-gnueabihf@4.1.8':
    optional: true

  '@tailwindcss/oxide-linux-arm64-gnu@4.1.7':
    optional: true

  '@tailwindcss/oxide-linux-arm64-gnu@4.1.8':
    optional: true

  '@tailwindcss/oxide-linux-arm64-musl@4.1.7':
    optional: true

  '@tailwindcss/oxide-linux-arm64-musl@4.1.8':
    optional: true

  '@tailwindcss/oxide-linux-x64-gnu@4.1.7':
    optional: true

  '@tailwindcss/oxide-linux-x64-gnu@4.1.8':
    optional: true

  '@tailwindcss/oxide-linux-x64-musl@4.1.7':
    optional: true

  '@tailwindcss/oxide-linux-x64-musl@4.1.8':
    optional: true

  '@tailwindcss/oxide-wasm32-wasi@4.1.7':
    optional: true

  '@tailwindcss/oxide-wasm32-wasi@4.1.8':
    optional: true

  '@tailwindcss/oxide-win32-arm64-msvc@4.1.7':
    optional: true

  '@tailwindcss/oxide-win32-arm64-msvc@4.1.8':
    optional: true

  '@tailwindcss/oxide-win32-x64-msvc@4.1.7':
    optional: true

  '@tailwindcss/oxide-win32-x64-msvc@4.1.8':
    optional: true

  '@tailwindcss/oxide@4.1.7':
    dependencies:
      detect-libc: 2.0.4
      tar: 7.4.3
    optionalDependencies:
      '@tailwindcss/oxide-android-arm64': 4.1.7
      '@tailwindcss/oxide-darwin-arm64': 4.1.7
      '@tailwindcss/oxide-darwin-x64': 4.1.7
      '@tailwindcss/oxide-freebsd-x64': 4.1.7
      '@tailwindcss/oxide-linux-arm-gnueabihf': 4.1.7
      '@tailwindcss/oxide-linux-arm64-gnu': 4.1.7
      '@tailwindcss/oxide-linux-arm64-musl': 4.1.7
      '@tailwindcss/oxide-linux-x64-gnu': 4.1.7
      '@tailwindcss/oxide-linux-x64-musl': 4.1.7
      '@tailwindcss/oxide-wasm32-wasi': 4.1.7
      '@tailwindcss/oxide-win32-arm64-msvc': 4.1.7
      '@tailwindcss/oxide-win32-x64-msvc': 4.1.7

  '@tailwindcss/oxide@4.1.8':
    dependencies:
      detect-libc: 2.0.4
      tar: 7.4.3
    optionalDependencies:
      '@tailwindcss/oxide-android-arm64': 4.1.8
      '@tailwindcss/oxide-darwin-arm64': 4.1.8
      '@tailwindcss/oxide-darwin-x64': 4.1.8
      '@tailwindcss/oxide-freebsd-x64': 4.1.8
      '@tailwindcss/oxide-linux-arm-gnueabihf': 4.1.8
      '@tailwindcss/oxide-linux-arm64-gnu': 4.1.8
      '@tailwindcss/oxide-linux-arm64-musl': 4.1.8
      '@tailwindcss/oxide-linux-x64-gnu': 4.1.8
      '@tailwindcss/oxide-linux-x64-musl': 4.1.8
      '@tailwindcss/oxide-wasm32-wasi': 4.1.8
      '@tailwindcss/oxide-win32-arm64-msvc': 4.1.8
      '@tailwindcss/oxide-win32-x64-msvc': 4.1.8

  '@tailwindcss/postcss@4.1.8':
    dependencies:
      '@alloc/quick-lru': 5.2.0
      '@tailwindcss/node': 4.1.8
      '@tailwindcss/oxide': 4.1.8
      postcss: 8.5.3
      tailwindcss: 4.1.8

  '@tailwindcss/vite@4.1.7(vite@6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0))':
    dependencies:
      '@tailwindcss/node': 4.1.7
      '@tailwindcss/oxide': 4.1.7
      tailwindcss: 4.1.7
      vite: 6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0)

  '@tanstack/eslint-plugin-query@5.74.7(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/utils': 8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)
      eslint: 9.27.0(jiti@2.4.2)
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@tanstack/query-core@5.76.0': {}

  '@tanstack/query-devtools@5.76.0': {}

  '@tanstack/react-query-devtools@5.76.1(@tanstack/react-query@5.76.1(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@tanstack/query-devtools': 5.76.0
      '@tanstack/react-query': 5.76.1(react@19.1.0)
      react: 19.1.0

  '@tanstack/react-query@5.76.1(react@19.1.0)':
    dependencies:
      '@tanstack/query-core': 5.76.0
      react: 19.1.0

  '@testing-library/dom@10.4.0':
    dependencies:
      '@babel/code-frame': 7.27.1
      '@babel/runtime': 7.27.1
      '@types/aria-query': 5.0.4
      aria-query: 5.3.0
      chalk: 4.1.2
      dom-accessibility-api: 0.5.16
      lz-string: 1.5.0
      pretty-format: 27.5.1

  '@testing-library/jest-dom@6.6.3':
    dependencies:
      '@adobe/css-tools': 4.4.3
      aria-query: 5.3.2
      chalk: 3.0.0
      css.escape: 1.5.1
      dom-accessibility-api: 0.6.3
      lodash: 4.17.21
      redent: 3.0.0

  '@testing-library/react@16.3.0(@testing-library/dom@10.4.0)(@types/react-dom@19.1.5(@types/react@19.1.5))(@types/react@19.1.5)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@babel/runtime': 7.27.1
      '@testing-library/dom': 10.4.0
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5
      '@types/react-dom': 19.1.5(@types/react@19.1.5)

  '@testing-library/user-event@14.6.1(@testing-library/dom@10.4.0)':
    dependencies:
      '@testing-library/dom': 10.4.0

  '@transloadit/prettier-bytes@0.3.5': {}

  '@tsparticles/basic@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1
      '@tsparticles/move-base': 3.8.1
      '@tsparticles/plugin-hex-color': 3.8.1
      '@tsparticles/plugin-hsl-color': 3.8.1
      '@tsparticles/plugin-rgb-color': 3.8.1
      '@tsparticles/shape-circle': 3.8.1
      '@tsparticles/updater-color': 3.8.1
      '@tsparticles/updater-opacity': 3.8.1
      '@tsparticles/updater-out-modes': 3.8.1
      '@tsparticles/updater-size': 3.8.1

  '@tsparticles/engine@3.8.1': {}

  '@tsparticles/interaction-external-attract@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/interaction-external-bounce@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/interaction-external-bubble@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/interaction-external-connect@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/interaction-external-grab@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/interaction-external-pause@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/interaction-external-push@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/interaction-external-remove@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/interaction-external-repulse@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/interaction-external-slow@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/interaction-external-trail@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/interaction-particles-attract@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/interaction-particles-collisions@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/interaction-particles-links@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/move-base@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/move-parallax@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/plugin-absorbers@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/plugin-easing-quad@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/plugin-emitters-shape-circle@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1
      '@tsparticles/plugin-emitters': 3.8.1

  '@tsparticles/plugin-emitters-shape-square@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1
      '@tsparticles/plugin-emitters': 3.8.1

  '@tsparticles/plugin-emitters@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/plugin-hex-color@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/plugin-hsl-color@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/plugin-rgb-color@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/react@3.0.0(@tsparticles/engine@3.8.1)(react-dom@19.1.0(react@19.1.0))(react@19.1.0)':
    dependencies:
      '@tsparticles/engine': 3.8.1
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  '@tsparticles/shape-circle@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/shape-emoji@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/shape-image@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/shape-line@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/shape-polygon@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/shape-square@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/shape-star@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/shape-text@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/slim@3.8.1':
    dependencies:
      '@tsparticles/basic': 3.8.1
      '@tsparticles/engine': 3.8.1
      '@tsparticles/interaction-external-attract': 3.8.1
      '@tsparticles/interaction-external-bounce': 3.8.1
      '@tsparticles/interaction-external-bubble': 3.8.1
      '@tsparticles/interaction-external-connect': 3.8.1
      '@tsparticles/interaction-external-grab': 3.8.1
      '@tsparticles/interaction-external-pause': 3.8.1
      '@tsparticles/interaction-external-push': 3.8.1
      '@tsparticles/interaction-external-remove': 3.8.1
      '@tsparticles/interaction-external-repulse': 3.8.1
      '@tsparticles/interaction-external-slow': 3.8.1
      '@tsparticles/interaction-particles-attract': 3.8.1
      '@tsparticles/interaction-particles-collisions': 3.8.1
      '@tsparticles/interaction-particles-links': 3.8.1
      '@tsparticles/move-parallax': 3.8.1
      '@tsparticles/plugin-easing-quad': 3.8.1
      '@tsparticles/shape-emoji': 3.8.1
      '@tsparticles/shape-image': 3.8.1
      '@tsparticles/shape-line': 3.8.1
      '@tsparticles/shape-polygon': 3.8.1
      '@tsparticles/shape-square': 3.8.1
      '@tsparticles/shape-star': 3.8.1
      '@tsparticles/updater-life': 3.8.1
      '@tsparticles/updater-rotate': 3.8.1
      '@tsparticles/updater-stroke-color': 3.8.1

  '@tsparticles/updater-color@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/updater-destroy@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/updater-life@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/updater-opacity@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/updater-out-modes@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/updater-roll@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/updater-rotate@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/updater-size@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/updater-stroke-color@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/updater-tilt@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/updater-twinkle@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@tsparticles/updater-wobble@3.8.1':
    dependencies:
      '@tsparticles/engine': 3.8.1

  '@types/aria-query@5.0.4': {}

  '@types/babel__core@7.20.5':
    dependencies:
      '@babel/parser': 7.27.2
      '@babel/types': 7.27.1
      '@types/babel__generator': 7.27.0
      '@types/babel__template': 7.4.4
      '@types/babel__traverse': 7.20.7

  '@types/babel__generator@7.27.0':
    dependencies:
      '@babel/types': 7.27.1

  '@types/babel__template@7.4.4':
    dependencies:
      '@babel/parser': 7.27.2
      '@babel/types': 7.27.1

  '@types/babel__traverse@7.20.7':
    dependencies:
      '@babel/types': 7.27.1

  '@types/chroma-js@3.1.1': {}

  '@types/conventional-commits-parser@5.0.1':
    dependencies:
      '@types/node': 22.15.21

  '@types/estree@1.0.7': {}

  '@types/json-schema@7.0.15': {}

  '@types/node@22.15.21':
    dependencies:
      undici-types: 6.21.0

  '@types/parse-json@4.0.2': {}

  '@types/prop-types@15.7.14': {}

  '@types/rbush@4.0.0': {}

  '@types/react-dom@19.1.5(@types/react@19.1.5)':
    dependencies:
      '@types/react': 19.1.5

  '@types/react-transition-group@4.4.12(@types/react@19.1.5)':
    dependencies:
      '@types/react': 19.1.5

  '@types/react@19.1.5':
    dependencies:
      csstype: 3.1.3

  '@types/retry@0.12.2': {}

  '@types/shimmer@1.2.0': {}

  '@typescript-eslint/eslint-plugin@8.32.1(@typescript-eslint/parser@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/scope-manager': 8.32.1
      '@typescript-eslint/type-utils': 8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/utils': 8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': 8.32.1
      eslint: 9.27.0(jiti@2.4.2)
      graphemer: 1.4.0
      ignore: 7.0.4
      natural-compare: 1.4.0
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/scope-manager': 8.32.1
      '@typescript-eslint/types': 8.32.1
      '@typescript-eslint/typescript-estree': 8.32.1(typescript@5.8.3)
      '@typescript-eslint/visitor-keys': 8.32.1
      debug: 4.4.1
      eslint: 9.27.0(jiti@2.4.2)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@8.32.1':
    dependencies:
      '@typescript-eslint/types': 8.32.1
      '@typescript-eslint/visitor-keys': 8.32.1

  '@typescript-eslint/type-utils@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/typescript-estree': 8.32.1(typescript@5.8.3)
      '@typescript-eslint/utils': 8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)
      debug: 4.4.1
      eslint: 9.27.0(jiti@2.4.2)
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@8.32.1': {}

  '@typescript-eslint/typescript-estree@8.32.1(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/types': 8.32.1
      '@typescript-eslint/visitor-keys': 8.32.1
      debug: 4.4.1
      fast-glob: 3.3.3
      is-glob: 4.0.3
      minimatch: 9.0.5
      semver: 7.7.2
      ts-api-utils: 2.1.0(typescript@5.8.3)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)':
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.27.0(jiti@2.4.2))
      '@typescript-eslint/scope-manager': 8.32.1
      '@typescript-eslint/types': 8.32.1
      '@typescript-eslint/typescript-estree': 8.32.1(typescript@5.8.3)
      eslint: 9.27.0(jiti@2.4.2)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/visitor-keys@8.32.1':
    dependencies:
      '@typescript-eslint/types': 8.32.1
      eslint-visitor-keys: 4.2.0

  '@uppy/aws-s3@4.2.3(@uppy/core@4.4.5)':
    dependencies:
      '@uppy/companion-client': 4.4.2(@uppy/core@4.4.5)
      '@uppy/core': 4.4.5
      '@uppy/utils': 6.1.4

  '@uppy/companion-client@4.4.2(@uppy/core@4.4.5)':
    dependencies:
      '@uppy/core': 4.4.5
      '@uppy/utils': 6.1.4
      namespace-emitter: 2.0.1
      p-retry: 6.2.1

  '@uppy/core@4.4.5':
    dependencies:
      '@transloadit/prettier-bytes': 0.3.5
      '@uppy/store-default': 4.2.0
      '@uppy/utils': 6.1.4
      lodash: 4.17.21
      mime-match: 1.0.2
      namespace-emitter: 2.0.1
      nanoid: 5.1.5
      preact: 10.26.6

  '@uppy/dashboard@4.3.4(@uppy/core@4.4.5)':
    dependencies:
      '@transloadit/prettier-bytes': 0.3.5
      '@uppy/core': 4.4.5
      '@uppy/informer': 4.2.1(@uppy/core@4.4.5)
      '@uppy/provider-views': 4.4.3(@uppy/core@4.4.5)
      '@uppy/status-bar': 4.1.3(@uppy/core@4.4.5)
      '@uppy/thumbnail-generator': 4.1.1(@uppy/core@4.4.5)
      '@uppy/utils': 6.1.4
      classnames: 2.5.1
      lodash: 4.17.21
      memoize-one: 6.0.0
      nanoid: 5.1.5
      preact: 10.26.6
      shallow-equal: 3.1.0

  '@uppy/drag-drop@4.1.3(@uppy/core@4.4.5)':
    dependencies:
      '@uppy/core': 4.4.5
      '@uppy/utils': 6.1.4
      preact: 10.26.6

  '@uppy/file-input@4.1.3(@uppy/core@4.4.5)':
    dependencies:
      '@uppy/core': 4.4.5
      '@uppy/utils': 6.1.4
      preact: 10.26.6

  '@uppy/informer@4.2.1(@uppy/core@4.4.5)':
    dependencies:
      '@uppy/core': 4.4.5
      '@uppy/utils': 6.1.4
      preact: 10.26.6

  '@uppy/locales@4.5.2':
    dependencies:
      '@uppy/utils': 6.1.4

  '@uppy/progress-bar@4.2.1(@uppy/core@4.4.5)':
    dependencies:
      '@uppy/core': 4.4.5
      '@uppy/utils': 6.1.4
      preact: 10.26.6

  '@uppy/provider-views@4.4.3(@uppy/core@4.4.5)':
    dependencies:
      '@uppy/core': 4.4.5
      '@uppy/utils': 6.1.4
      classnames: 2.5.1
      nanoid: 5.1.5
      p-queue: 8.1.0
      preact: 10.26.6

  '@uppy/react@4.2.3(@uppy/core@4.4.5)(@uppy/dashboard@4.3.4(@uppy/core@4.4.5))(@uppy/drag-drop@4.1.3(@uppy/core@4.4.5))(@uppy/file-input@4.1.3(@uppy/core@4.4.5))(@uppy/progress-bar@4.2.1(@uppy/core@4.4.5))(@uppy/status-bar@4.1.3(@uppy/core@4.4.5))(react@19.1.0)':
    dependencies:
      '@uppy/core': 4.4.5
      '@uppy/utils': 6.1.4
      react: 19.1.0
      use-sync-external-store: 1.5.0(react@19.1.0)
    optionalDependencies:
      '@uppy/dashboard': 4.3.4(@uppy/core@4.4.5)
      '@uppy/drag-drop': 4.1.3(@uppy/core@4.4.5)
      '@uppy/file-input': 4.1.3(@uppy/core@4.4.5)
      '@uppy/progress-bar': 4.2.1(@uppy/core@4.4.5)
      '@uppy/status-bar': 4.1.3(@uppy/core@4.4.5)

  '@uppy/status-bar@4.1.3(@uppy/core@4.4.5)':
    dependencies:
      '@transloadit/prettier-bytes': 0.3.5
      '@uppy/core': 4.4.5
      '@uppy/utils': 6.1.4
      classnames: 2.5.1
      preact: 10.26.6

  '@uppy/store-default@4.2.0': {}

  '@uppy/thumbnail-generator@4.1.1(@uppy/core@4.4.5)':
    dependencies:
      '@uppy/core': 4.4.5
      '@uppy/utils': 6.1.4
      exifr: 7.1.3

  '@uppy/utils@6.1.4':
    dependencies:
      lodash: 4.17.21
      preact: 10.26.6

  '@vitejs/plugin-react@4.4.1(vite@6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0))':
    dependencies:
      '@babel/core': 7.27.1
      '@babel/plugin-transform-react-jsx-self': 7.27.1(@babel/core@7.27.1)
      '@babel/plugin-transform-react-jsx-source': 7.27.1(@babel/core@7.27.1)
      '@types/babel__core': 7.20.5
      react-refresh: 0.17.0
      vite: 6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0)
    transitivePeerDependencies:
      - supports-color

  '@vitest/expect@3.1.4':
    dependencies:
      '@vitest/spy': 3.1.4
      '@vitest/utils': 3.1.4
      chai: 5.2.0
      tinyrainbow: 2.0.0

  '@vitest/mocker@3.1.4(vite@6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0))':
    dependencies:
      '@vitest/spy': 3.1.4
      estree-walker: 3.0.3
      magic-string: 0.30.17
    optionalDependencies:
      vite: 6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0)

  '@vitest/pretty-format@3.1.4':
    dependencies:
      tinyrainbow: 2.0.0

  '@vitest/runner@3.1.4':
    dependencies:
      '@vitest/utils': 3.1.4
      pathe: 2.0.3

  '@vitest/snapshot@3.1.4':
    dependencies:
      '@vitest/pretty-format': 3.1.4
      magic-string: 0.30.17
      pathe: 2.0.3

  '@vitest/spy@3.1.4':
    dependencies:
      tinyspy: 3.0.2

  '@vitest/utils@3.1.4':
    dependencies:
      '@vitest/pretty-format': 3.1.4
      loupe: 3.1.3
      tinyrainbow: 2.0.0

  JSONStream@1.3.5:
    dependencies:
      jsonparse: 1.3.1
      through: 2.3.8

  acorn-import-attributes@1.9.5(acorn@8.14.1):
    dependencies:
      acorn: 8.14.1

  acorn-jsx@5.3.2(acorn@8.14.1):
    dependencies:
      acorn: 8.14.1

  acorn@8.14.1: {}

  agent-base@7.1.3: {}

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  ansi-escapes@7.0.0:
    dependencies:
      environment: 1.1.0

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0: {}

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@5.2.0: {}

  ansi-styles@6.2.1: {}

  argparse@2.0.1: {}

  aria-hidden@1.2.6:
    dependencies:
      tslib: 2.8.1

  aria-query@5.3.0:
    dependencies:
      dequal: 2.0.3

  aria-query@5.3.2: {}

  array-buffer-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      is-array-buffer: 3.0.5

  array-ify@1.0.0: {}

  array-includes@3.1.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      is-string: 1.1.1

  array.prototype.findlast@1.2.5:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-shim-unscopables: 1.1.0

  array.prototype.flat@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-shim-unscopables: 1.1.0

  array.prototype.flatmap@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-shim-unscopables: 1.1.0

  array.prototype.tosorted@1.1.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-shim-unscopables: 1.1.0

  arraybuffer.prototype.slice@1.0.4:
    dependencies:
      array-buffer-byte-length: 1.0.2
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      is-array-buffer: 3.0.5

  assertion-error@2.0.1: {}

  async-function@1.0.0: {}

  asynckit@0.4.0: {}

  attr-accept@2.2.5: {}

  autoprefixer@10.4.21(postcss@8.5.3):
    dependencies:
      browserslist: 4.24.5
      caniuse-lite: 1.0.30001718
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.1.1
      postcss: 8.5.3
      postcss-value-parser: 4.2.0

  autosuggest-highlight@3.3.4:
    dependencies:
      remove-accents: 0.4.4

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.1.0

  axios@1.9.0:
    dependencies:
      follow-redirects: 1.15.9
      form-data: 4.0.2
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  babel-plugin-macros@3.1.0:
    dependencies:
      '@babel/runtime': 7.27.1
      cosmiconfig: 7.1.0
      resolve: 1.22.10

  balanced-match@1.0.2: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist@4.24.5:
    dependencies:
      caniuse-lite: 1.0.30001718
      electron-to-chromium: 1.5.155
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.24.5)

  cac@6.7.14: {}

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bind@1.0.8:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      get-intrinsic: 1.3.0
      set-function-length: 1.2.2

  call-bound@1.0.4:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  callsites@3.1.0: {}

  caniuse-lite@1.0.30001718: {}

  chai@5.2.0:
    dependencies:
      assertion-error: 2.0.1
      check-error: 2.1.1
      deep-eql: 5.0.2
      loupe: 3.1.3
      pathval: 2.0.0

  chalk@3.0.0:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@5.4.1: {}

  check-error@2.1.1: {}

  chownr@3.0.0: {}

  cjs-module-lexer@1.4.3: {}

  class-variance-authority@0.7.1:
    dependencies:
      clsx: 2.1.1

  classnames@2.5.1: {}

  cli-cursor@5.0.0:
    dependencies:
      restore-cursor: 5.1.0

  cli-truncate@4.0.0:
    dependencies:
      slice-ansi: 5.0.0
      string-width: 7.2.0

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clsx@2.1.1: {}

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  colorette@2.0.20: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@13.1.0: {}

  compare-func@2.0.0:
    dependencies:
      array-ify: 1.0.0
      dot-prop: 5.3.0

  concat-map@0.0.1: {}

  conventional-changelog-angular@7.0.0:
    dependencies:
      compare-func: 2.0.0

  conventional-changelog-conventionalcommits@7.0.2:
    dependencies:
      compare-func: 2.0.0

  conventional-commits-parser@5.0.0:
    dependencies:
      JSONStream: 1.3.5
      is-text-path: 2.0.0
      meow: 12.1.1
      split2: 4.2.0

  convert-source-map@1.9.0: {}

  convert-source-map@2.0.0: {}

  cosmiconfig-typescript-loader@6.1.0(@types/node@22.15.21)(cosmiconfig@9.0.0(typescript@5.8.3))(typescript@5.8.3):
    dependencies:
      '@types/node': 22.15.21
      cosmiconfig: 9.0.0(typescript@5.8.3)
      jiti: 2.4.2
      typescript: 5.8.3

  cosmiconfig@7.1.0:
    dependencies:
      '@types/parse-json': 4.0.2
      import-fresh: 3.3.1
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2

  cosmiconfig@9.0.0(typescript@5.8.3):
    dependencies:
      env-paths: 2.2.1
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      parse-json: 5.2.0
    optionalDependencies:
      typescript: 5.8.3

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  css.escape@1.5.1: {}

  cssstyle@4.3.1:
    dependencies:
      '@asamuzakjp/css-color': 3.2.0
      rrweb-cssom: 0.8.0

  csstype@3.1.3: {}

  dargs@8.1.0: {}

  data-urls@5.0.0:
    dependencies:
      whatwg-mimetype: 4.0.0
      whatwg-url: 14.2.0

  data-view-buffer@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-offset@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-data-view: 1.0.2

  date-fns-tz@3.2.0(date-fns@4.1.0):
    dependencies:
      date-fns: 4.1.0

  date-fns@4.1.0: {}

  debug@4.4.1:
    dependencies:
      ms: 2.1.3

  decimal.js@10.5.0: {}

  deep-eql@5.0.2: {}

  deep-is@0.1.4: {}

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  delayed-stream@1.0.0: {}

  dequal@2.0.3: {}

  detect-libc@2.0.4: {}

  detect-node-es@1.1.0: {}

  doctrine@2.1.0:
    dependencies:
      esutils: 2.0.3

  dom-accessibility-api@0.5.16: {}

  dom-accessibility-api@0.6.3: {}

  dom-helpers@5.2.1:
    dependencies:
      '@babel/runtime': 7.27.1
      csstype: 3.1.3

  dot-prop@5.3.0:
    dependencies:
      is-obj: 2.0.0

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  earcut@3.0.1: {}

  electron-to-chromium@1.5.155: {}

  emoji-regex@10.4.0: {}

  emoji-regex@8.0.0: {}

  enhanced-resolve@5.18.1:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.2

  entities@6.0.0: {}

  env-paths@2.2.1: {}

  environment@1.1.0: {}

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  es-abstract@1.23.9:
    dependencies:
      array-buffer-byte-length: 1.0.2
      arraybuffer.prototype.slice: 1.0.4
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      data-view-buffer: 1.0.2
      data-view-byte-length: 1.0.2
      data-view-byte-offset: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-set-tostringtag: 2.1.0
      es-to-primitive: 1.3.0
      function.prototype.name: 1.1.8
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      get-symbol-description: 1.1.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      internal-slot: 1.1.0
      is-array-buffer: 3.0.5
      is-callable: 1.2.7
      is-data-view: 1.0.2
      is-regex: 1.2.1
      is-shared-array-buffer: 1.0.4
      is-string: 1.1.1
      is-typed-array: 1.1.15
      is-weakref: 1.1.1
      math-intrinsics: 1.1.0
      object-inspect: 1.13.4
      object-keys: 1.1.1
      object.assign: 4.1.7
      own-keys: 1.0.1
      regexp.prototype.flags: 1.5.4
      safe-array-concat: 1.1.3
      safe-push-apply: 1.0.0
      safe-regex-test: 1.1.0
      set-proto: 1.0.0
      string.prototype.trim: 1.2.10
      string.prototype.trimend: 1.0.9
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.3
      typed-array-byte-length: 1.0.3
      typed-array-byte-offset: 1.0.4
      typed-array-length: 1.0.7
      unbox-primitive: 1.1.0
      which-typed-array: 1.1.19

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-iterator-helpers@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-set-tostringtag: 2.1.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      iterator.prototype: 1.1.5
      safe-array-concat: 1.1.3

  es-module-lexer@1.7.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-shim-unscopables@1.1.0:
    dependencies:
      hasown: 2.0.2

  es-to-primitive@1.3.0:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.1.0
      is-symbol: 1.1.1

  esbuild@0.25.4:
    optionalDependencies:
      '@esbuild/aix-ppc64': 0.25.4
      '@esbuild/android-arm': 0.25.4
      '@esbuild/android-arm64': 0.25.4
      '@esbuild/android-x64': 0.25.4
      '@esbuild/darwin-arm64': 0.25.4
      '@esbuild/darwin-x64': 0.25.4
      '@esbuild/freebsd-arm64': 0.25.4
      '@esbuild/freebsd-x64': 0.25.4
      '@esbuild/linux-arm': 0.25.4
      '@esbuild/linux-arm64': 0.25.4
      '@esbuild/linux-ia32': 0.25.4
      '@esbuild/linux-loong64': 0.25.4
      '@esbuild/linux-mips64el': 0.25.4
      '@esbuild/linux-ppc64': 0.25.4
      '@esbuild/linux-riscv64': 0.25.4
      '@esbuild/linux-s390x': 0.25.4
      '@esbuild/linux-x64': 0.25.4
      '@esbuild/netbsd-arm64': 0.25.4
      '@esbuild/netbsd-x64': 0.25.4
      '@esbuild/openbsd-arm64': 0.25.4
      '@esbuild/openbsd-x64': 0.25.4
      '@esbuild/sunos-x64': 0.25.4
      '@esbuild/win32-arm64': 0.25.4
      '@esbuild/win32-ia32': 0.25.4
      '@esbuild/win32-x64': 0.25.4

  escalade@3.2.0: {}

  escape-string-regexp@4.0.0: {}

  eslint-config-prettier@10.1.5(eslint@9.27.0(jiti@2.4.2)):
    dependencies:
      eslint: 9.27.0(jiti@2.4.2)

  eslint-plugin-import-helpers@2.0.1(eslint@9.27.0(jiti@2.4.2)):
    dependencies:
      eslint: 9.27.0(jiti@2.4.2)

  eslint-plugin-prettier@5.4.0(eslint-config-prettier@10.1.5(eslint@9.27.0(jiti@2.4.2)))(eslint@9.27.0(jiti@2.4.2))(prettier@3.5.3):
    dependencies:
      eslint: 9.27.0(jiti@2.4.2)
      prettier: 3.5.3
      prettier-linter-helpers: 1.0.0
      synckit: 0.11.6
    optionalDependencies:
      eslint-config-prettier: 10.1.5(eslint@9.27.0(jiti@2.4.2))

  eslint-plugin-react-hooks@5.2.0(eslint@9.27.0(jiti@2.4.2)):
    dependencies:
      eslint: 9.27.0(jiti@2.4.2)

  eslint-plugin-react-refresh@0.4.20(eslint@9.27.0(jiti@2.4.2)):
    dependencies:
      eslint: 9.27.0(jiti@2.4.2)

  eslint-plugin-react@7.37.5(eslint@9.27.0(jiti@2.4.2)):
    dependencies:
      array-includes: 3.1.8
      array.prototype.findlast: 1.2.5
      array.prototype.flatmap: 1.3.3
      array.prototype.tosorted: 1.1.4
      doctrine: 2.1.0
      es-iterator-helpers: 1.2.1
      eslint: 9.27.0(jiti@2.4.2)
      estraverse: 5.3.0
      hasown: 2.0.2
      jsx-ast-utils: 3.3.5
      minimatch: 3.1.2
      object.entries: 1.1.9
      object.fromentries: 2.0.8
      object.values: 1.2.1
      prop-types: 15.8.1
      resolve: 2.0.0-next.5
      semver: 6.3.1
      string.prototype.matchall: 4.0.12
      string.prototype.repeat: 1.0.0

  eslint-plugin-unused-imports@4.1.4(@typescript-eslint/eslint-plugin@8.32.1(@typescript-eslint/parser@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.27.0(jiti@2.4.2)):
    dependencies:
      eslint: 9.27.0(jiti@2.4.2)
    optionalDependencies:
      '@typescript-eslint/eslint-plugin': 8.32.1(@typescript-eslint/parser@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)

  eslint-scope@8.3.0:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-visitor-keys@3.4.3: {}

  eslint-visitor-keys@4.2.0: {}

  eslint@9.27.0(jiti@2.4.2):
    dependencies:
      '@eslint-community/eslint-utils': 4.7.0(eslint@9.27.0(jiti@2.4.2))
      '@eslint-community/regexpp': 4.12.1
      '@eslint/config-array': 0.20.0
      '@eslint/config-helpers': 0.2.2
      '@eslint/core': 0.14.0
      '@eslint/eslintrc': 3.3.1
      '@eslint/js': 9.27.0
      '@eslint/plugin-kit': 0.3.1
      '@humanfs/node': 0.16.6
      '@humanwhocodes/module-importer': 1.0.1
      '@humanwhocodes/retry': 0.4.3
      '@types/estree': 1.0.7
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.1
      escape-string-regexp: 4.0.0
      eslint-scope: 8.3.0
      eslint-visitor-keys: 4.2.0
      espree: 10.3.0
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 8.0.0
      find-up: 5.0.0
      glob-parent: 6.0.2
      ignore: 5.3.2
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      json-stable-stringify-without-jsonify: 1.0.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
    optionalDependencies:
      jiti: 2.4.2
    transitivePeerDependencies:
      - supports-color

  espree@10.3.0:
    dependencies:
      acorn: 8.14.1
      acorn-jsx: 5.3.2(acorn@8.14.1)
      eslint-visitor-keys: 4.2.0

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@5.3.0: {}

  estree-walker@3.0.3:
    dependencies:
      '@types/estree': 1.0.7

  esutils@2.0.3: {}

  eventemitter3@5.0.1: {}

  execa@8.0.1:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 8.0.1
      human-signals: 5.0.0
      is-stream: 3.0.0
      merge-stream: 2.0.0
      npm-run-path: 5.3.0
      onetime: 6.0.0
      signal-exit: 4.1.0
      strip-final-newline: 3.0.0

  exifr@7.1.3: {}

  expect-type@1.2.1: {}

  fast-deep-equal@3.1.3: {}

  fast-diff@1.3.0: {}

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-uri@3.0.6: {}

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  fdir@6.4.4(picomatch@4.0.2):
    optionalDependencies:
      picomatch: 4.0.2

  file-entry-cache@8.0.0:
    dependencies:
      flat-cache: 4.0.1

  file-selector@2.1.2:
    dependencies:
      tslib: 2.8.1

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  find-root@1.1.0: {}

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  find-up@7.0.0:
    dependencies:
      locate-path: 7.2.0
      path-exists: 5.0.0
      unicorn-magic: 0.1.0

  flat-cache@4.0.1:
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4

  flatted@3.3.3: {}

  follow-redirects@1.15.9: {}

  for-each@0.3.5:
    dependencies:
      is-callable: 1.2.7

  form-data@4.0.2:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      mime-types: 2.1.35

  fraction.js@4.3.7: {}

  framer-motion@12.12.1(@emotion/is-prop-valid@1.3.1)(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      motion-dom: 12.12.1
      motion-utils: 12.12.1
      tslib: 2.8.1
    optionalDependencies:
      '@emotion/is-prop-valid': 1.3.1
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  fsevents@2.3.2:
    optional: true

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  function.prototype.name@1.1.8:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      functions-have-names: 1.2.3
      hasown: 2.0.2
      is-callable: 1.2.7

  functions-have-names@1.2.3: {}

  gensync@1.0.0-beta.2: {}

  geotiff@2.1.3:
    dependencies:
      '@petamoriken/float16': 3.9.2
      lerc: 3.0.0
      pako: 2.1.0
      parse-headers: 2.0.6
      quick-lru: 6.1.2
      web-worker: 1.5.0
      xml-utils: 1.10.2
      zstddec: 0.1.0

  get-caller-file@2.0.5: {}

  get-east-asian-width@1.3.0: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-nonce@1.0.1: {}

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-stream@8.0.1: {}

  get-symbol-description@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0

  git-raw-commits@4.0.0:
    dependencies:
      dargs: 8.1.0
      meow: 12.1.1
      split2: 4.2.0

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  global-directory@4.0.1:
    dependencies:
      ini: 4.1.1

  global-jsdom@26.0.0(jsdom@26.1.0):
    dependencies:
      jsdom: 26.1.0

  globals@11.12.0: {}

  globals@14.0.0: {}

  globals@15.15.0: {}

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.2.0

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  has-bigints@1.1.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.1

  has-proto@1.2.0:
    dependencies:
      dunder-proto: 1.0.1

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  hoist-non-react-statics@3.3.2:
    dependencies:
      react-is: 16.13.1

  html-encoding-sniffer@4.0.0:
    dependencies:
      whatwg-encoding: 3.1.1

  http-proxy-agent@7.0.2:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@7.0.6:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.1
    transitivePeerDependencies:
      - supports-color

  human-signals@5.0.0: {}

  husky@9.1.7: {}

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  ignore@5.3.2: {}

  ignore@7.0.4: {}

  immer@10.1.1: {}

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  import-in-the-middle@1.13.2:
    dependencies:
      acorn: 8.14.1
      acorn-import-attributes: 1.9.5(acorn@8.14.1)
      cjs-module-lexer: 1.4.3
      module-details-from-path: 1.0.4

  import-meta-resolve@4.1.0: {}

  imurmurhash@0.1.4: {}

  indent-string@4.0.0: {}

  ini@4.1.1: {}

  internal-slot@1.1.0:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.1.0

  is-array-buffer@3.0.5:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  is-arrayish@0.2.1: {}

  is-async-function@2.1.1:
    dependencies:
      async-function: 1.0.0
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-bigint@1.1.0:
    dependencies:
      has-bigints: 1.1.0

  is-boolean-object@1.2.2:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-callable@1.2.7: {}

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.2:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      is-typed-array: 1.1.15

  is-date-object@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-extglob@2.1.1: {}

  is-finalizationregistry@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-fullwidth-code-point@3.0.0: {}

  is-fullwidth-code-point@4.0.0: {}

  is-fullwidth-code-point@5.0.0:
    dependencies:
      get-east-asian-width: 1.3.0

  is-generator-function@1.1.0:
    dependencies:
      call-bound: 1.0.4
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-map@2.0.3: {}

  is-network-error@1.1.0: {}

  is-number-object@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-number@7.0.0: {}

  is-obj@2.0.0: {}

  is-potential-custom-element-name@1.0.1: {}

  is-regex@1.2.1:
    dependencies:
      call-bound: 1.0.4
      gopd: 1.2.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  is-set@2.0.3: {}

  is-shared-array-buffer@1.0.4:
    dependencies:
      call-bound: 1.0.4

  is-stream@3.0.0: {}

  is-string@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-tostringtag: 1.0.2

  is-symbol@1.1.1:
    dependencies:
      call-bound: 1.0.4
      has-symbols: 1.1.0
      safe-regex-test: 1.1.0

  is-text-path@2.0.0:
    dependencies:
      text-extensions: 2.4.0

  is-typed-array@1.1.15:
    dependencies:
      which-typed-array: 1.1.19

  is-weakmap@2.0.2: {}

  is-weakref@1.1.1:
    dependencies:
      call-bound: 1.0.4

  is-weakset@2.0.4:
    dependencies:
      call-bound: 1.0.4
      get-intrinsic: 1.3.0

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  iterator.prototype@1.1.5:
    dependencies:
      define-data-property: 1.1.4
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      has-symbols: 1.1.0
      set-function-name: 2.0.2

  jiti@2.4.2: {}

  js-tokens@4.0.0: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsdom@26.1.0:
    dependencies:
      cssstyle: 4.3.1
      data-urls: 5.0.0
      decimal.js: 10.5.0
      html-encoding-sniffer: 4.0.0
      http-proxy-agent: 7.0.2
      https-proxy-agent: 7.0.6
      is-potential-custom-element-name: 1.0.1
      nwsapi: 2.2.20
      parse5: 7.3.0
      rrweb-cssom: 0.8.0
      saxes: 6.0.0
      symbol-tree: 3.2.4
      tough-cookie: 5.1.2
      w3c-xmlserializer: 5.0.0
      webidl-conversions: 7.0.0
      whatwg-encoding: 3.1.1
      whatwg-mimetype: 4.0.0
      whatwg-url: 14.2.0
      ws: 8.18.2
      xml-name-validator: 5.0.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json-stringify-pretty-compact@4.0.0: {}

  json5@2.2.3: {}

  jsonparse@1.3.1: {}

  jsx-ast-utils@3.3.5:
    dependencies:
      array-includes: 3.1.8
      array.prototype.flat: 1.3.3
      object.assign: 4.1.7
      object.values: 1.2.1

  jwt-decode@4.0.0: {}

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  lerc@3.0.0: {}

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lightningcss-darwin-arm64@1.30.1:
    optional: true

  lightningcss-darwin-x64@1.30.1:
    optional: true

  lightningcss-freebsd-x64@1.30.1:
    optional: true

  lightningcss-linux-arm-gnueabihf@1.30.1:
    optional: true

  lightningcss-linux-arm64-gnu@1.30.1:
    optional: true

  lightningcss-linux-arm64-musl@1.30.1:
    optional: true

  lightningcss-linux-x64-gnu@1.30.1:
    optional: true

  lightningcss-linux-x64-musl@1.30.1:
    optional: true

  lightningcss-win32-arm64-msvc@1.30.1:
    optional: true

  lightningcss-win32-x64-msvc@1.30.1:
    optional: true

  lightningcss@1.30.1:
    dependencies:
      detect-libc: 2.0.4
    optionalDependencies:
      lightningcss-darwin-arm64: 1.30.1
      lightningcss-darwin-x64: 1.30.1
      lightningcss-freebsd-x64: 1.30.1
      lightningcss-linux-arm-gnueabihf: 1.30.1
      lightningcss-linux-arm64-gnu: 1.30.1
      lightningcss-linux-arm64-musl: 1.30.1
      lightningcss-linux-x64-gnu: 1.30.1
      lightningcss-linux-x64-musl: 1.30.1
      lightningcss-win32-arm64-msvc: 1.30.1
      lightningcss-win32-x64-msvc: 1.30.1

  lilconfig@3.1.3: {}

  lines-and-columns@1.2.4: {}

  lint-staged@15.5.2:
    dependencies:
      chalk: 5.4.1
      commander: 13.1.0
      debug: 4.4.1
      execa: 8.0.1
      lilconfig: 3.1.3
      listr2: 8.3.3
      micromatch: 4.0.8
      pidtree: 0.6.0
      string-argv: 0.3.2
      yaml: 2.8.0
    transitivePeerDependencies:
      - supports-color

  listr2@8.3.3:
    dependencies:
      cli-truncate: 4.0.0
      colorette: 2.0.20
      eventemitter3: 5.0.1
      log-update: 6.1.0
      rfdc: 1.4.1
      wrap-ansi: 9.0.0

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  locate-path@7.2.0:
    dependencies:
      p-locate: 6.0.0

  lodash.camelcase@4.3.0: {}

  lodash.isplainobject@4.0.6: {}

  lodash.kebabcase@4.1.1: {}

  lodash.merge@4.6.2: {}

  lodash.mergewith@4.6.2: {}

  lodash.snakecase@4.1.1: {}

  lodash.startcase@4.4.0: {}

  lodash.uniq@4.5.0: {}

  lodash.upperfirst@4.3.1: {}

  lodash@4.17.21: {}

  log-update@6.1.0:
    dependencies:
      ansi-escapes: 7.0.0
      cli-cursor: 5.0.0
      slice-ansi: 7.1.0
      strip-ansi: 7.1.0
      wrap-ansi: 9.0.0

  long@5.3.2: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  loupe@3.1.3: {}

  lru-cache@10.4.3: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lucide-react@0.475.0(react@19.1.0):
    dependencies:
      react: 19.1.0

  lucide-react@0.476.0(react@19.1.0):
    dependencies:
      react: 19.1.0

  lz-string@1.5.0: {}

  magic-string@0.30.17:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.5.0

  mapbox-to-css-font@3.2.0: {}

  masonic@4.1.0(react@19.1.0):
    dependencies:
      '@essentials/memoize-one': 1.1.0
      '@essentials/one-key-map': 1.2.0
      '@essentials/request-timeout': 1.3.0
      '@react-hook/event': 1.2.6(react@19.1.0)
      '@react-hook/latest': 1.0.3(react@19.1.0)
      '@react-hook/passive-layout-effect': 1.2.1(react@19.1.0)
      '@react-hook/throttle': 2.2.0(react@19.1.0)
      '@react-hook/window-scroll': 1.3.0(react@19.1.0)
      '@react-hook/window-size': 3.1.1(react@19.1.0)
      raf-schd: 4.0.3
      react: 19.1.0
      trie-memoize: 1.2.0

  math-intrinsics@1.1.0: {}

  memoize-one@6.0.0: {}

  meow@12.1.1: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-match@1.0.2:
    dependencies:
      wildcard: 1.1.2

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mimic-fn@4.0.0: {}

  mimic-function@5.0.1: {}

  min-indent@1.0.1: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  minipass@7.1.2: {}

  minizlib@3.0.2:
    dependencies:
      minipass: 7.1.2

  mkdirp@3.0.1: {}

  module-details-from-path@1.0.4: {}

  motion-dom@12.12.1:
    dependencies:
      motion-utils: 12.12.1

  motion-utils@12.12.1: {}

  ms@2.1.3: {}

  namespace-emitter@2.0.1: {}

  nanoid@3.3.11: {}

  nanoid@5.1.5: {}

  natural-compare@1.4.0: {}

  node-releases@2.0.19: {}

  normalize-range@0.1.2: {}

  npm-run-path@5.3.0:
    dependencies:
      path-key: 4.0.0

  nwsapi@2.2.20: {}

  object-assign@4.1.1: {}

  object-inspect@1.13.4: {}

  object-keys@1.1.1: {}

  object.assign@4.1.7:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
      has-symbols: 1.1.0
      object-keys: 1.1.1

  object.entries@1.1.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  object.fromentries@2.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1

  object.values@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  ol-mapbox-style@12.6.1(ol@10.5.0):
    dependencies:
      '@maplibre/maplibre-gl-style-spec': 23.3.0
      mapbox-to-css-font: 3.2.0
      ol: 10.5.0

  ol@10.5.0:
    dependencies:
      '@types/rbush': 4.0.0
      earcut: 3.0.1
      geotiff: 2.1.3
      pbf: 4.0.1
      rbush: 4.0.1

  onetime@6.0.0:
    dependencies:
      mimic-fn: 4.0.0

  onetime@7.0.0:
    dependencies:
      mimic-function: 5.0.1

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  own-keys@1.0.1:
    dependencies:
      get-intrinsic: 1.3.0
      object-keys: 1.1.1
      safe-push-apply: 1.0.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-limit@4.0.0:
    dependencies:
      yocto-queue: 1.2.1

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  p-locate@6.0.0:
    dependencies:
      p-limit: 4.0.0

  p-queue@8.1.0:
    dependencies:
      eventemitter3: 5.0.1
      p-timeout: 6.1.4

  p-retry@6.2.1:
    dependencies:
      '@types/retry': 0.12.2
      is-network-error: 1.1.0
      retry: 0.13.1

  p-timeout@6.1.4: {}

  pako@2.1.0: {}

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-headers@2.0.6: {}

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.27.1
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse5@7.3.0:
    dependencies:
      entities: 6.0.0

  path-exists@4.0.0: {}

  path-exists@5.0.0: {}

  path-key@3.1.1: {}

  path-key@4.0.0: {}

  path-parse@1.0.7: {}

  path-type@4.0.0: {}

  pathe@2.0.3: {}

  pathval@2.0.0: {}

  pbf@4.0.1:
    dependencies:
      resolve-protobuf-schema: 2.1.0

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  picomatch@4.0.2: {}

  pidtree@0.6.0: {}

  playwright-core@1.52.0: {}

  playwright@1.52.0:
    dependencies:
      playwright-core: 1.52.0
    optionalDependencies:
      fsevents: 2.3.2

  possible-typed-array-names@1.1.0: {}

  postcss-value-parser@4.2.0: {}

  postcss@8.5.3:
    dependencies:
      nanoid: 3.3.11
      picocolors: 1.1.1
      source-map-js: 1.2.1

  preact@10.26.6: {}

  prelude-ls@1.2.1: {}

  prettier-linter-helpers@1.0.0:
    dependencies:
      fast-diff: 1.3.0

  prettier@3.5.3: {}

  pretty-format@27.5.1:
    dependencies:
      ansi-regex: 5.0.1
      ansi-styles: 5.2.0
      react-is: 17.0.2

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  protobufjs@7.4.0:
    dependencies:
      '@protobufjs/aspromise': 1.1.2
      '@protobufjs/base64': 1.1.2
      '@protobufjs/codegen': 2.0.4
      '@protobufjs/eventemitter': 1.1.0
      '@protobufjs/fetch': 1.1.0
      '@protobufjs/float': 1.0.2
      '@protobufjs/inquire': 1.1.0
      '@protobufjs/path': 1.1.2
      '@protobufjs/pool': 1.1.0
      '@protobufjs/utf8': 1.1.0
      '@types/node': 22.15.21
      long: 5.3.2

  protocol-buffers-schema@3.6.0: {}

  proxy-from-env@1.1.0: {}

  punycode@2.3.1: {}

  queue-microtask@1.2.3: {}

  quick-lru@6.1.2: {}

  quickselect@3.0.0: {}

  raf-schd@4.0.3: {}

  rbush@4.0.1:
    dependencies:
      quickselect: 3.0.0

  react-day-picker@8.10.1(date-fns@4.1.0)(react@19.1.0):
    dependencies:
      date-fns: 4.1.0
      react: 19.1.0

  react-dom@19.1.0(react@19.1.0):
    dependencies:
      react: 19.1.0
      scheduler: 0.26.0

  react-dropzone@14.3.8(react@19.1.0):
    dependencies:
      attr-accept: 2.2.5
      file-selector: 2.1.2
      prop-types: 15.8.1
      react: 19.1.0

  react-hook-form@7.56.4(react@19.1.0):
    dependencies:
      react: 19.1.0

  react-icons@5.5.0(react@19.1.0):
    dependencies:
      react: 19.1.0

  react-is@16.13.1: {}

  react-is@17.0.2: {}

  react-is@19.1.0: {}

  react-layout-masonry@1.2.0(react@19.1.0):
    dependencies:
      react: 19.1.0

  react-photo-album@3.1.0(@types/react@19.1.5)(react@19.1.0):
    dependencies:
      react: 19.1.0
    optionalDependencies:
      '@types/react': 19.1.5

  react-refresh@0.17.0: {}

  react-remove-scroll-bar@2.3.8(@types/react@19.1.5)(react@19.1.0):
    dependencies:
      react: 19.1.0
      react-style-singleton: 2.2.3(@types/react@19.1.5)(react@19.1.0)
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.5

  react-remove-scroll@2.7.0(@types/react@19.1.5)(react@19.1.0):
    dependencies:
      react: 19.1.0
      react-remove-scroll-bar: 2.3.8(@types/react@19.1.5)(react@19.1.0)
      react-style-singleton: 2.2.3(@types/react@19.1.5)(react@19.1.0)
      tslib: 2.8.1
      use-callback-ref: 1.3.3(@types/react@19.1.5)(react@19.1.0)
      use-sidecar: 1.1.3(@types/react@19.1.5)(react@19.1.0)
    optionalDependencies:
      '@types/react': 19.1.5

  react-responsive-masonry@2.7.1: {}

  react-router-dom@6.30.1(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      '@remix-run/router': 1.23.0
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)
      react-router: 6.30.1(react@19.1.0)

  react-router@6.30.1(react@19.1.0):
    dependencies:
      '@remix-run/router': 1.23.0
      react: 19.1.0

  react-style-singleton@2.2.3(@types/react@19.1.5)(react@19.1.0):
    dependencies:
      get-nonce: 1.0.1
      react: 19.1.0
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.5

  react-transition-group@4.4.5(react-dom@19.1.0(react@19.1.0))(react@19.1.0):
    dependencies:
      '@babel/runtime': 7.27.1
      dom-helpers: 5.2.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 19.1.0
      react-dom: 19.1.0(react@19.1.0)

  react@19.1.0: {}

  redent@3.0.0:
    dependencies:
      indent-string: 4.0.0
      strip-indent: 3.0.0

  reflect.getprototypeof@1.0.10:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      which-builtin-type: 1.2.1

  regexp.prototype.flags@1.5.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-errors: 1.3.0
      get-proto: 1.0.1
      gopd: 1.2.0
      set-function-name: 2.0.2

  remove-accents@0.4.4: {}

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  require-in-the-middle@7.5.2:
    dependencies:
      debug: 4.4.1
      module-details-from-path: 1.0.4
      resolve: 1.22.10
    transitivePeerDependencies:
      - supports-color

  resolve-from@4.0.0: {}

  resolve-from@5.0.0: {}

  resolve-protobuf-schema@2.1.0:
    dependencies:
      protocol-buffers-schema: 3.6.0

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resolve@2.0.0-next.5:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restore-cursor@5.1.0:
    dependencies:
      onetime: 7.0.0
      signal-exit: 4.1.0

  retry@0.13.1: {}

  reusify@1.1.0: {}

  rfdc@1.4.1: {}

  rollup@4.41.0:
    dependencies:
      '@types/estree': 1.0.7
    optionalDependencies:
      '@rollup/rollup-android-arm-eabi': 4.41.0
      '@rollup/rollup-android-arm64': 4.41.0
      '@rollup/rollup-darwin-arm64': 4.41.0
      '@rollup/rollup-darwin-x64': 4.41.0
      '@rollup/rollup-freebsd-arm64': 4.41.0
      '@rollup/rollup-freebsd-x64': 4.41.0
      '@rollup/rollup-linux-arm-gnueabihf': 4.41.0
      '@rollup/rollup-linux-arm-musleabihf': 4.41.0
      '@rollup/rollup-linux-arm64-gnu': 4.41.0
      '@rollup/rollup-linux-arm64-musl': 4.41.0
      '@rollup/rollup-linux-loongarch64-gnu': 4.41.0
      '@rollup/rollup-linux-powerpc64le-gnu': 4.41.0
      '@rollup/rollup-linux-riscv64-gnu': 4.41.0
      '@rollup/rollup-linux-riscv64-musl': 4.41.0
      '@rollup/rollup-linux-s390x-gnu': 4.41.0
      '@rollup/rollup-linux-x64-gnu': 4.41.0
      '@rollup/rollup-linux-x64-musl': 4.41.0
      '@rollup/rollup-win32-arm64-msvc': 4.41.0
      '@rollup/rollup-win32-ia32-msvc': 4.41.0
      '@rollup/rollup-win32-x64-msvc': 4.41.0
      fsevents: 2.3.3

  rrweb-cssom@0.8.0: {}

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  rw@1.3.3: {}

  safe-array-concat@1.1.3:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      get-intrinsic: 1.3.0
      has-symbols: 1.1.0
      isarray: 2.0.5

  safe-push-apply@1.0.0:
    dependencies:
      es-errors: 1.3.0
      isarray: 2.0.5

  safe-regex-test@1.1.0:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-regex: 1.2.1

  safer-buffer@2.1.2: {}

  saxes@6.0.0:
    dependencies:
      xmlchars: 2.2.0

  scheduler@0.26.0: {}

  semver@6.3.1: {}

  semver@7.7.2: {}

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  set-proto@1.0.0:
    dependencies:
      dunder-proto: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1

  shallow-equal@3.1.0: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  shimmer@1.2.1: {}

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  siginfo@2.0.0: {}

  signal-exit@4.1.0: {}

  slice-ansi@5.0.0:
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 4.0.0

  slice-ansi@7.1.0:
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 5.0.0

  source-map-js@1.2.1: {}

  source-map@0.5.7: {}

  split2@4.2.0: {}

  stackback@0.0.2: {}

  std-env@3.9.0: {}

  string-argv@0.3.2: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@7.2.0:
    dependencies:
      emoji-regex: 10.4.0
      get-east-asian-width: 1.3.0
      strip-ansi: 7.1.0

  string.prototype.matchall@4.0.12:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      regexp.prototype.flags: 1.5.4
      set-function-name: 2.0.2
      side-channel: 1.1.0

  string.prototype.repeat@1.0.0:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.23.9

  string.prototype.trim@1.2.10:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-data-property: 1.1.4
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1
      has-property-descriptors: 1.0.2

  string.prototype.trimend@1.0.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.4
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0

  strip-final-newline@3.0.0: {}

  strip-indent@3.0.0:
    dependencies:
      min-indent: 1.0.1

  strip-json-comments@3.1.1: {}

  stylis@4.2.0: {}

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  swiper@11.2.7: {}

  symbol-tree@3.2.4: {}

  synckit@0.11.6:
    dependencies:
      '@pkgr/core': 0.2.4

  tailwind-merge@3.3.0: {}

  tailwindcss-animate@1.0.7(tailwindcss@4.1.7):
    dependencies:
      tailwindcss: 4.1.7

  tailwindcss@4.1.7: {}

  tailwindcss@4.1.8: {}

  tapable@2.2.2: {}

  tar@7.4.3:
    dependencies:
      '@isaacs/fs-minipass': 4.0.1
      chownr: 3.0.0
      minipass: 7.1.2
      minizlib: 3.0.2
      mkdirp: 3.0.1
      yallist: 5.0.0

  text-extensions@2.4.0: {}

  through@2.3.8: {}

  tinybench@2.9.0: {}

  tinyexec@0.3.2: {}

  tinyexec@1.0.1: {}

  tinyglobby@0.2.13:
    dependencies:
      fdir: 6.4.4(picomatch@4.0.2)
      picomatch: 4.0.2

  tinypool@1.0.2: {}

  tinyqueue@3.0.0: {}

  tinyrainbow@2.0.0: {}

  tinyspy@3.0.2: {}

  tldts-core@6.1.86: {}

  tldts@6.1.86:
    dependencies:
      tldts-core: 6.1.86

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  tough-cookie@5.1.2:
    dependencies:
      tldts: 6.1.86

  tr46@5.1.1:
    dependencies:
      punycode: 2.3.1

  trie-memoize@1.2.0: {}

  ts-api-utils@2.1.0(typescript@5.8.3):
    dependencies:
      typescript: 5.8.3

  tslib@2.8.1: {}

  tsparticles@3.8.1:
    dependencies:
      '@tsparticles/engine': 3.8.1
      '@tsparticles/interaction-external-trail': 3.8.1
      '@tsparticles/plugin-absorbers': 3.8.1
      '@tsparticles/plugin-emitters': 3.8.1
      '@tsparticles/plugin-emitters-shape-circle': 3.8.1
      '@tsparticles/plugin-emitters-shape-square': 3.8.1
      '@tsparticles/shape-text': 3.8.1
      '@tsparticles/slim': 3.8.1
      '@tsparticles/updater-destroy': 3.8.1
      '@tsparticles/updater-roll': 3.8.1
      '@tsparticles/updater-tilt': 3.8.1
      '@tsparticles/updater-twinkle': 3.8.1
      '@tsparticles/updater-wobble': 3.8.1

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  typed-array-buffer@1.0.3:
    dependencies:
      call-bound: 1.0.4
      es-errors: 1.3.0
      is-typed-array: 1.1.15

  typed-array-byte-length@1.0.3:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15

  typed-array-byte-offset@1.0.4:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
      reflect.getprototypeof: 1.0.10

  typed-array-length@1.0.7:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      is-typed-array: 1.1.15
      possible-typed-array-names: 1.1.0
      reflect.getprototypeof: 1.0.10

  typescript-eslint@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3):
    dependencies:
      '@typescript-eslint/eslint-plugin': 8.32.1(@typescript-eslint/parser@8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3))(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/parser': 8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)
      '@typescript-eslint/utils': 8.32.1(eslint@9.27.0(jiti@2.4.2))(typescript@5.8.3)
      eslint: 9.27.0(jiti@2.4.2)
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  typescript@5.8.3: {}

  ua-parser-js@1.0.40: {}

  unbox-primitive@1.1.0:
    dependencies:
      call-bound: 1.0.4
      has-bigints: 1.1.0
      has-symbols: 1.1.0
      which-boxed-primitive: 1.1.1

  undici-types@6.21.0: {}

  unicorn-magic@0.1.0: {}

  update-browserslist-db@1.1.3(browserslist@4.24.5):
    dependencies:
      browserslist: 4.24.5
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  use-callback-ref@1.3.3(@types/react@19.1.5)(react@19.1.0):
    dependencies:
      react: 19.1.0
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.5

  use-sidecar@1.1.3(@types/react@19.1.5)(react@19.1.0):
    dependencies:
      detect-node-es: 1.1.0
      react: 19.1.0
      tslib: 2.8.1
    optionalDependencies:
      '@types/react': 19.1.5

  use-sync-external-store@1.5.0(react@19.1.0):
    dependencies:
      react: 19.1.0

  vite-node@3.1.4(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0):
    dependencies:
      cac: 6.7.14
      debug: 4.4.1
      es-module-lexer: 1.7.0
      pathe: 2.0.3
      vite: 6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0)
    transitivePeerDependencies:
      - '@types/node'
      - jiti
      - less
      - lightningcss
      - sass
      - sass-embedded
      - stylus
      - sugarss
      - supports-color
      - terser
      - tsx
      - yaml

  vite@6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0):
    dependencies:
      esbuild: 0.25.4
      fdir: 6.4.4(picomatch@4.0.2)
      picomatch: 4.0.2
      postcss: 8.5.3
      rollup: 4.41.0
      tinyglobby: 0.2.13
    optionalDependencies:
      '@types/node': 22.15.21
      fsevents: 2.3.3
      jiti: 2.4.2
      lightningcss: 1.30.1
      yaml: 2.8.0

  vitest@3.1.4(@types/node@22.15.21)(jiti@2.4.2)(jsdom@26.1.0)(lightningcss@1.30.1)(yaml@2.8.0):
    dependencies:
      '@vitest/expect': 3.1.4
      '@vitest/mocker': 3.1.4(vite@6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0))
      '@vitest/pretty-format': 3.1.4
      '@vitest/runner': 3.1.4
      '@vitest/snapshot': 3.1.4
      '@vitest/spy': 3.1.4
      '@vitest/utils': 3.1.4
      chai: 5.2.0
      debug: 4.4.1
      expect-type: 1.2.1
      magic-string: 0.30.17
      pathe: 2.0.3
      std-env: 3.9.0
      tinybench: 2.9.0
      tinyexec: 0.3.2
      tinyglobby: 0.2.13
      tinypool: 1.0.2
      tinyrainbow: 2.0.0
      vite: 6.3.5(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0)
      vite-node: 3.1.4(@types/node@22.15.21)(jiti@2.4.2)(lightningcss@1.30.1)(yaml@2.8.0)
      why-is-node-running: 2.3.0
    optionalDependencies:
      '@types/node': 22.15.21
      jsdom: 26.1.0
    transitivePeerDependencies:
      - jiti
      - less
      - lightningcss
      - msw
      - sass
      - sass-embedded
      - stylus
      - sugarss
      - supports-color
      - terser
      - tsx
      - yaml

  w3c-xmlserializer@5.0.0:
    dependencies:
      xml-name-validator: 5.0.0

  web-vitals@4.2.4: {}

  web-worker@1.5.0: {}

  webidl-conversions@7.0.0: {}

  whatwg-encoding@3.1.1:
    dependencies:
      iconv-lite: 0.6.3

  whatwg-mimetype@4.0.0: {}

  whatwg-url@14.2.0:
    dependencies:
      tr46: 5.1.1
      webidl-conversions: 7.0.0

  which-boxed-primitive@1.1.1:
    dependencies:
      is-bigint: 1.1.0
      is-boolean-object: 1.2.2
      is-number-object: 1.1.1
      is-string: 1.1.1
      is-symbol: 1.1.1

  which-builtin-type@1.2.1:
    dependencies:
      call-bound: 1.0.4
      function.prototype.name: 1.1.8
      has-tostringtag: 1.0.2
      is-async-function: 2.1.1
      is-date-object: 1.1.0
      is-finalizationregistry: 1.1.1
      is-generator-function: 1.1.0
      is-regex: 1.2.1
      is-weakref: 1.1.1
      isarray: 2.0.5
      which-boxed-primitive: 1.1.1
      which-collection: 1.0.2
      which-typed-array: 1.1.19

  which-collection@1.0.2:
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.4

  which-typed-array@1.1.19:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.4
      for-each: 0.3.5
      get-proto: 1.0.1
      gopd: 1.2.0
      has-tostringtag: 1.0.2

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  why-is-node-running@2.3.0:
    dependencies:
      siginfo: 2.0.0
      stackback: 0.0.2

  wildcard@1.1.2: {}

  word-wrap@1.2.5: {}

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@9.0.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 7.2.0
      strip-ansi: 7.1.0

  ws@8.18.2: {}

  xml-name-validator@5.0.0: {}

  xml-utils@1.10.2: {}

  xmlchars@2.2.0: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yallist@5.0.0: {}

  yaml@1.10.2: {}

  yaml@2.8.0: {}

  yargs-parser@21.1.1: {}

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yocto-queue@0.1.0: {}

  yocto-queue@1.2.1: {}

  zstddec@0.1.0: {}

  zustand@5.0.5(@types/react@19.1.5)(immer@10.1.1)(react@19.1.0)(use-sync-external-store@1.5.0(react@19.1.0)):
    optionalDependencies:
      '@types/react': 19.1.5
      immer: 10.1.1
      react: 19.1.0
      use-sync-external-store: 1.5.0(react@19.1.0)
