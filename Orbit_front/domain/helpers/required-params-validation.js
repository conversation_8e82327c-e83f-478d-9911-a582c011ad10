/**
 * Validates that all required parameters are present in the given data object.
 *
 * @param {Object} data - The data object to be validated.
 * @param {string[]} requiredParams - An array of required parameter names.
 * @throws Will throw an error if data is not an object.
 * @throws Will throw an error if any required parameter is missing from the data object.
 */
export const RequiredParamsValidation = (data, requiredParams) => {
  if (!data || typeof data !== "object") {
    throw new Error("Invalid data: Data must be an object.");
  }
  
  requiredParams.forEach(param => {
    if (!data[param]) {
      throw new Error(`Invalid data at ${data.id ?? ""}: Missing required parameter "${param}".`);
    }
  })
}
