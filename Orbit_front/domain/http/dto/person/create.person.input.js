/**
 * Transforms formState into the input payload format.
 * @param {Object} formState - The input form state containing all main and aggregate attributes.
 * @returns {Object} - Object formatted like new-person-input-dto.json
 */
export function transformPersonsFormStateToInputDTO(formState) {
  const payload = {
    properties: {},
    aggregates: []
  };

  console.log('transformPersonsFormStateToInputDTO: formState -> ', formState)
  Object.keys(formState).forEach((key) => {
    const value = formState[key];
    if (value === "") return;

    // Check if the value relates to an aggregate (array or object structure)
    if (Array.isArray(value)) {
      // Handle aggregate arrays
      value.forEach((item) => {
        let nodeName = key
        if (key === "pessoa") nodeName = item["nome_completo"] || item["razao_social"]
        payload.aggregates.push({ 
            name: nodeName,
            type: key,
            properties: item
        });
      });
    }
      // else if (typeof value === "object" && value !== null) {
      //   // Handle single aggregate objects (if needed)
      //   payload.aggregates.push({
      //     node_class: "entity",
      //     node_data: {
      //       name: key,
      //       type: key,
      //       properties: value
      //     }
      //   });
    // }
    else {
      // Handle main_node attributes (primitive values)
      switch (key) {
        case "nome_completo":
        case "razao_social":
          payload.name = value;
          payload.type = "pessoa";
          break;
        default:
          payload.properties[key] = value;
          break;
      }
    }
  });

  return payload;
}

// Example usage
// const formState = {
//   nome_completo: "Victor Rommel Salles de Almeida",
//   data_nascimento: "1991-08-31T03:00:00.000Z",
//   estado_civil: "casado",
//   cpf: "14577432722",
//   identidade: "",
//   pis_pasep_nis: "",
//   nacionalidade: "brasileiro",
//   naturalidade: "rio de janeiro",
//   escolaridade: "medio completo",
//   campo_livre_anotacoes: "",
//   status_alvo: "ativo",
//   anotacoes_internas: "",
//   emails: [
//     {
//       email: "<EMAIL>",
//       fonte_dados: "dispositivo movel"
//     },
//     {
//       email: "<EMAIL>",
//       fonte_dados: "computador corporativo"
//     }
//   ],
//   enderecos_ip: [
//     {
//       ip: "127.0.0.1:1127",
//       fonte_dados: "scripts pessoais"
//     }
//   ],
//   localizacoes_frequentes: [
//     {
//       cep: "25060210",
//       rua: "Rua Camorim",
//       numero: "124",
//       complemento: "casa 04",
//       bairro: "Dr Laureano",
//       cidade: "Duque de Caxias",
//       estado: "RJ"
//     }
//   ]
// };
//
// const outputPayload = transformPersonsFormStateToInputDTO(formState);
//
// console.log(JSON.stringify(outputPayload, null, 2));