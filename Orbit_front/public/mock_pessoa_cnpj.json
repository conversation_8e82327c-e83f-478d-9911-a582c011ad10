[{"name": "razao_social", "label": "Razão Social", "type": "text"}, {"name": "nome_fantasia", "label": "Nome Fantasia", "type": "text"}, {"name": "data_fundacao", "label": "Data de Fundação", "type": "date", "value": ""}, {"name": "cnpj", "label": "CNPJ", "type": "text"}, {"name": "inscricao_estadual", "label": "Inscrição Estadual", "type": "text"}, {"name": "natureza_juridica", "label": "Natureza Jurídica", "type": "text"}, {"name": "cnae", "label": "CNAE", "type": "array", "fields": [{"name": "tipo", "label": "Tipo", "type": "text"}, {"name": "numero", "label": "Número", "type": "text"}]}, {"name": "campo_livre_anotacoes", "label": "Campo Livre Anotações", "type": "multiline"}, {"name": "status_alvo", "label": "Status do Alvo", "type": "select", "options": [{"value": "ativo", "label": "Ativo"}, {"value": "inativo", "label": "Inativo"}, {"value": "monitorado", "label": "Sob monitoramento"}, {"value": "concluido", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"name": "anotacoes_internas", "label": "Anotações Internas", "type": "multiline"}, {"name": "enderecos", "label": "Endereços", "type": "array", "fields": [{"name": "cep", "label": "CEP", "type": "text"}, {"name": "logradouro", "label": "Logradouro", "type": "text"}, {"name": "numero", "label": "Número", "type": "text"}, {"name": "bairro", "label": "Bairro", "type": "text"}, {"name": "cidade", "label": "Cidade", "type": "text"}, {"name": "estado", "label": "Estado", "type": "text"}, {"name": "pais", "label": "<PERSON><PERSON>", "type": "text"}, {"name": "complemento", "label": "Complemento", "type": "text"}, {"name": "latitude", "label": "Latitude", "type": "text"}, {"name": "longitude", "label": "Longitude", "type": "text"}]}, {"name": "telefones", "label": "Telefones", "type": "array", "fields": [{"name": "telefone", "label": "Telefone", "type": "text"}, {"name": "fonte_dados", "label": "Fonte de Dados", "type": "text"}]}, {"name": "emails", "label": "Emails", "type": "array", "fields": [{"name": "email", "label": "Email", "type": "text"}, {"name": "fonte_dados", "label": "Fonte de Dados", "type": "text"}]}, {"name": "socios", "label": "<PERSON><PERSON><PERSON><PERSON>", "type": "array", "fields": [{"name": "nome_completo", "label": "Nome <PERSON>to", "type": "text"}, {"name": "cpf", "label": "CPF", "type": "text"}, {"name": "participacao", "label": "Participação", "type": "text"}]}, {"name": "filiais", "label": "<PERSON><PERSON><PERSON>", "type": "array", "fields": [{"name": "razao_social", "label": "Razão Social", "type": "text"}, {"name": "cnpj", "label": "CNPJ", "type": "text"}]}, {"name": "sociedades", "label": "Sociedades", "type": "array", "fields": [{"name": "razao_social", "label": "Razão Social", "type": "text"}, {"name": "cnpj", "label": "CNPJ", "type": "text"}, {"name": "data_entrada", "label": "Data de Entrada", "type": "date"}, {"name": "participacao", "label": "Participação", "type": "text"}]}, {"name": "imagens", "label": "Imagens", "type": "array", "fields": [{"name": "link", "label": "Link", "type": "text"}, {"name": "file", "label": "Arquivo", "type": "file"}]}, {"name": "propried<PERSON>_bens_imoveis", "label": "Propriedades/Bens Imóveis", "type": "array", "fields": [{"name": "numero_matricula", "label": "Número de Matrícula", "type": "text"}, {"name": "numero_inscricao_imobiliaria", "label": "Número de Inscrição Imobiliária", "type": "text"}, {"name": "tipo_imovel", "label": "Tipo de Imóvel", "type": "text"}, {"name": "valor_avaliacao", "label": "Valor de Avaliação", "type": "text"}, {"name": "data_aquisicao", "label": "Data de Aquisição", "type": "date"}, {"name": "cartorio_registro", "label": "Cartório de Registro", "type": "text"}, {"name": "fotos", "label": "Fotos", "type": "file"}]}, {"name": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "array", "fields": [{"name": "modelo", "label": "<PERSON><PERSON>", "type": "text"}, {"name": "ano", "label": "<PERSON><PERSON>", "type": "text"}, {"name": "placa", "label": "Placa", "type": "text"}]}, {"name": "registros_penitenciarios", "label": "Registros Penitenciários", "type": "array", "fields": [{"name": "tipo_ocorrencia", "label": "Tipo de Ocorrência", "type": "select", "options": [{"value": "ingresso", "label": "Ingresso"}, {"value": "reingresso", "label": "Reingresso"}, {"value": "transferencia", "label": "Transferência Externa"}, {"value": "liberdade", "label": "Liberdade"}, {"value": "fuga", "label": "Fuga"}, {"value": "evasao", "label": "Evasão"}, {"value": "falecimento", "label": "Falecimento"}]}, {"name": "data", "label": "Data", "type": "date"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "label": "Detalhamento", "type": "text"}]}, {"name": "registros_financeiros_fiduciarios", "label": "Registros Financeiros/Fiduciários", "type": "array", "fields": [{"name": "nome_titular", "label": "Nome do Titular", "type": "text"}, {"name": "cpf_cnpj", "label": "CPF/CNPJ", "type": "text"}, {"name": "instituicao_financeira", "label": "Instituição Financeira", "type": "text"}, {"name": "numero_conta", "label": "Número da Conta", "type": "text"}, {"name": "agencia_bancaria", "label": "<PERSON><PERSON><PERSON><PERSON>", "type": "text"}, {"name": "tipo_conta", "label": "Tipo de Conta", "type": "text"}, {"name": "data_abertura_conta", "label": "Data de Abertura da Conta", "type": "date"}, {"name": "beneficiarios", "label": "Beneficiários", "type": "text"}]}, {"name": "registros_financeiros_cripto", "label": "Registros Financeiros Criptomoedas", "type": "array", "fields": [{"name": "nome_titular", "label": "Nome do Titular", "type": "text"}, {"name": "cpf_cnpj", "label": "CPF/CNPJ", "type": "text"}, {"name": "<PERSON><PERSON>", "label": "Car<PERSON>ira (Wallet Address)", "type": "text"}, {"name": "exchange", "label": "Exchange", "type": "text"}, {"name": "chave_publica", "label": "Chave <PERSON>", "type": "text"}, {"name": "data_criacao_carteira", "label": "Data de Criação da Carteira", "type": "date"}, {"name": "saldo_atual", "label": "<PERSON><PERSON>", "type": "text"}, {"name": "valor_moeda_fiat", "label": "Valor em Moeda Fiat", "type": "text"}]}, {"name": "man<PERSON><PERSON>_de_prisao", "label": "Mandados De Prisão", "type": "array", "fields": [{"name": "numero_mandato", "label": "Número do Mandato", "type": "text"}, {"name": "situacao", "label": "Situação", "type": "text"}, {"name": "numero_processo", "label": "Número do Processo", "type": "text"}, {"name": "tipificacoes_penais", "label": "Tipificaçõ<PERSON>", "type": "text"}, {"name": "data_expedicao", "label": "Data de Expedição", "type": "date"}, {"name": "data_validade", "label": "Data de Validade", "type": "date"}, {"name": "especie_prisao", "label": "Espécie de Prisão", "type": "select", "options": [{"value": "preventiva", "label": "Preventiva"}]}, {"name": "orgao_expedidor", "label": "Órgão Expedidor", "type": "text"}, {"name": "magistrado", "label": "<PERSON><PERSON><PERSON><PERSON>", "type": "text"}, {"name": "pena_imposta", "label": "<PERSON><PERSON>", "type": "text"}, {"name": "recaptura", "label": "Recaptura", "type": "select", "options": [{"value": "sim", "label": "<PERSON>m"}, {"value": "nao", "label": "Não"}]}]}, {"name": "antecedentes_criminais", "label": "Antecedentes Criminais", "type": "array", "fields": [{"name": "delegacia", "label": "Delegacia", "type": "text"}, {"name": "data", "label": "Data", "type": "date"}, {"name": "numero_processo", "label": "Número do Processo", "type": "text"}, {"name": "ano_processo", "label": "Ano do Processo", "type": "text"}, {"name": "crime", "label": "Crime", "type": "text"}, {"name": "status", "label": "Status", "type": "select", "options": [{"value": "a<PERSON>ando", "label": "Aguardando"}, {"value": "suspensao", "label": "Suspensão"}, {"value": "concluido", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"name": "vara_criminal", "label": "Vara Criminal", "type": "text"}, {"name": "oficio", "label": "<PERSON><PERSON><PERSON>", "type": "text"}, {"name": "tipo_ocorrencia", "label": "Tipo de Ocorrência", "type": "text"}]}, {"name": "noticias", "label": "Notícias", "type": "array", "fields": [{"name": "link", "label": "Link", "type": "text"}]}, {"name": "processos_judiciais", "label": "Processos <PERSON>", "type": "array", "fields": [{"name": "numero_processo", "label": "Número Do Processo", "type": "text"}, {"name": "data_instauracao", "label": "Data De Instauração", "type": "date"}, {"name": "data_remessa", "label": "Data De Re<PERSON>sa", "type": "date"}, {"name": "orgao", "label": "Orgão", "type": "text"}, {"name": "instancia", "label": "Instância", "type": "text"}, {"name": "movimentacoes", "label": "Movimentações", "type": "text"}]}, {"name": "faccao_grupo_criminoso", "label": "Facção / Grupo Criminoso", "type": "array", "fields": [{"name": "nome", "label": "Nome", "type": "text"}]}, {"name": "relint", "label": "Relatórios De Inteligência", "type": "array", "fields": [{"name": "nome", "label": "Nome", "type": "file"}]}, {"name": "rifs", "label": "Relatório De Informação Financeira", "type": "array", "fields": [{"name": "nome", "label": "Nome", "type": "file"}]}]