{"name": "orbit-governanca", "private": true, "version": "0.0.0", "type": "module", "scripts": {"preinstall": "npx --yes only-allow pnpm", "dev": "vite", "build": "vite build", "lint": "eslint --fix", "preview": "vite preview", "prepare": "husky", "test": "vitest", "test:run": "vitest run", "test:staged": "vitest related --run", "test:ci": "vitest run --coverage", "test:e2e": "npx playwright test --ui", "test:e2e:ci": "cross-env CI=true npm exec playwright test --pass-with-no-tests", "test:e2e:install": "npm exec playwright install firefox", "precommit": "lint-staged"}, "volta": {"node": "22.13.1", "npm": "10.9.2"}, "packageManager": "pnpm@10.5.1", "engines": {"pnpm": ">=10"}, "dependencies": {"@dvsl/zoomcharts": "1.21.4", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@grafana/faro-web-sdk": "^1.13.3", "@grafana/faro-web-tracing": "^1.13.3", "@mui/icons-material": "^7.1.0", "@mui/lab": "^7.0.0-beta.12", "@mui/material": "^7.1.0", "@mui/utils": "^7.1.0", "@mui/x-date-pickers": "^8.3.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@snap/design-system": "^1.2.4", "@tailwindcss/vite": "^4.0.6", "@tanstack/react-query": "^5.61.3", "@tsparticles/engine": "^3.7.1", "@tsparticles/react": "^3.0.0", "@uppy/aws-s3": "^4.2.1", "@uppy/core": "^4.4.1", "@uppy/dashboard": "^4.3.4", "@uppy/drag-drop": "^4.1.3", "@uppy/file-input": "^4.1.3", "@uppy/locales": "^4.2.0", "@uppy/progress-bar": "^4.0.0", "@uppy/react": "^4.2.1", "autosuggest-highlight": "^3.3.4", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "framer-motion": "^12.6.2", "immer": "^10.1.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.475.0", "ol": "^10.3.1", "ol-mapbox-style": "^12.4.0", "prop-types": "^15.8.1", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-dropzone": "^14.2.9", "react-hook-form": "^7.53.0", "react-icons": "^5.3.0", "react-router-dom": "^6.26.2", "swiper": "^11.1.14", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "tsparticles": "^3.7.1", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@eslint/js": "^9.9.0", "@playwright/test": "^1.50.1", "@tanstack/eslint-plugin-query": "^5.61.3", "@tanstack/react-query-devtools": "^5.61.3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/chroma-js": "^3.1.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import-helpers": "^2.0.1", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-unused-imports": "^4.1.4", "global-jsdom": "^26.0.0", "globals": "^15.15.0", "husky": "^9.1.7", "jsdom": "^26.0.0", "lint-staged": "^15.4.3", "postcss": "^8.5.2", "tailwindcss": "^4.0.6", "typescript": "^5.7.3", "typescript-eslint": "^8.24.0", "vite": "^6.1.0", "vitest": "^3.0.5"}}